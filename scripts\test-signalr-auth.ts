/**
 * Test script to verify SignalR authentication
 * This script can be used to test the SignalR connection with proper authentication
 */

import { SignalRChatService } from '@/services/api/chats/signalr-chat.service';
import { apiClient } from '@/services/api/base/api-client';
import { ApiLogger } from '@/services/api/base/api-logger';
import * as signalR from '@microsoft/signalr';

/**
 * Test SignalR connection with authentication
 */
export async function testSignalRAuth(): Promise<void> {
  try {
    console.log('🔧 Testing SignalR Authentication...');
    
    // Test the access token factory
    const accessTokenFactory = async (): Promise<string> => {
      try {
        const tokenData = await apiClient.getValidToken();
        if (tokenData?.accessToken && tokenData?.tokenType) {
          const formattedToken = `${tokenData.tokenType} ${tokenData.accessToken}`;
          console.log('✅ Token factory success:', {
            tokenType: tokenData.tokenType,
            hasAccessToken: !!tokenData.accessToken,
            tokenLength: tokenData.accessToken.length,
            formattedTokenPrefix: formattedToken.substring(0, 20) + '...'
          });
          return formattedToken;
        }
        throw new Error('No valid token data available');
      } catch (error) {
        console.error('❌ Token factory failed:', error);
        throw error;
      }
    };

    // Test token factory directly
    console.log('🔍 Testing token factory...');
    try {
      const token = await accessTokenFactory();
      console.log('✅ Token factory test passed');
    } catch (error) {
      console.error('❌ Token factory test failed:', error);
      return;
    }

    // Create SignalR service
    const hubUrl = process.env.EXPO_PUBLIC_API_BASE_URL?.replace(/\/api$/, '') + '/hubs/chat';
    console.log('🌐 Hub URL:', hubUrl);

    const service = new SignalRChatService({
      hubUrl,
      accessTokenFactory,
      automaticReconnect: true,
      logLevel: signalR.LogLevel.Debug
    });

    // Set up event handlers for testing
    service.setEventHandlers({
      onConnectionStateChanged: (state) => {
        console.log('🔄 Connection state changed:', state);
      }
    });

    // Initialize and connect
    console.log('🚀 Initializing SignalR service...');
    await service.initialize();
    
    console.log('🔗 Connecting to SignalR hub...');
    await service.connect();

    if (service.isConnected()) {
      console.log('✅ SignalR connection successful!');
      
      // Test ListenChats call
      console.log('📡 Testing ListenChats...');
      // The connect method already calls ListenChats, so we just verify the connection
      
      console.log('🧹 Cleaning up...');
      await service.disconnect();
      console.log('✅ Test completed successfully!');
    } else {
      console.error('❌ SignalR connection failed');
    }

  } catch (error) {
    console.error('❌ SignalR authentication test failed:', error);
    throw error;
  }
}

/**
 * Test token format validation
 */
export function testTokenFormat(): void {
  console.log('🔍 Testing token format validation...');
  
  const testCases = [
    { tokenType: 'Bearer', accessToken: 'abc123', expected: 'Bearer abc123' },
    { tokenType: 'bearer', accessToken: 'xyz789', expected: 'bearer xyz789' },
    { tokenType: 'JWT', accessToken: 'jwt.token.here', expected: 'JWT jwt.token.here' }
  ];

  testCases.forEach((testCase, index) => {
    const formatted = `${testCase.tokenType} ${testCase.accessToken}`;
    const passed = formatted === testCase.expected;
    console.log(`Test ${index + 1}: ${passed ? '✅' : '❌'}`, {
      input: testCase,
      output: formatted,
      expected: testCase.expected,
      passed
    });
  });
}

// Export for use in development/testing
export default {
  testSignalRAuth,
  testTokenFormat
};
