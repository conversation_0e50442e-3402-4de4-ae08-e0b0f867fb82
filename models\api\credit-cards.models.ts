/**
 * Credit Card models based on ClubM API
 * Matches the API schemas from swagger.json
 */

import { z } from 'zod';
import { PaginationRequest } from './common.models';

// API Request interfaces (based on CreateCreditCardViewModel)
export interface CreateCreditCardRequest {
  holderName: string;
  number: string;
  expiryMonth: number;
  expiryYear: number;
  cvv: string;
}

// API Response interfaces (based on CreditCardViewModel)
export interface CreditCard {
  id: number;
  holderName: string;
  lastFourDigits: string;
  expiryMonth: number;
  expiryYear: number;
  brand: string;
  userId: number;
  createdAt: string;
  createdBy: string;
}

// Paginated response (based on CreditCardViewModelPaginateViewModel)
export interface CreditCardsResponse {
  data: CreditCard[];
  totalItems: number;
  page: number;
  pageSize: number;
  pageCount: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

// Request parameters for listing credit cards
export interface CreditCardsListParams extends PaginationRequest {
  // API only supports Page and PageSize parameters
}

// Validation schemas with Zod
export const CreateCreditCardSchema = z.object({
  holderName: z.string().min(2, "Nome do portador é obrigatório").max(100),
  number: z.string()
    .min(13, "Número do cartão deve ter pelo menos 13 dígitos")
    .max(19, "Número do cartão deve ter no máximo 19 dígitos")
    .regex(/^\d+$/, "Número do cartão deve conter apenas dígitos"),
  expiryMonth: z.number()
    .min(1, "Mês deve ser entre 1 e 12")
    .max(12, "Mês deve ser entre 1 e 12"),
  expiryYear: z.number()
    .min(new Date().getFullYear(), "Ano não pode ser no passado")
    .max(new Date().getFullYear() + 20, "Ano muito distante no futuro"),
  cvv: z.string()
    .min(3, "CVV deve ter pelo menos 3 dígitos")
    .max(4, "CVV deve ter no máximo 4 dígitos")
    .regex(/^\d+$/, "CVV deve conter apenas dígitos")
});

// Derived types
export type CreateCreditCardFormData = z.infer<typeof CreateCreditCardSchema>;

// UI Display interfaces (for transforming API data to existing UI components)
export interface MemberCard {
  id: string;
  name: string;
  photo: string;
  membershipType: string;
  cardNumber: string;
  federationUnit: string;
  associationUnit: string;
  activeSince: string;
  isDefault: boolean;
  cardType: "member" | "american-express" | "visa";
  expiryDate?: string;
}

// Transformation utilities interface
export interface CreditCardTransformOptions {
  defaultPhoto?: string;
  defaultMembershipType?: string;
  defaultFederationUnit?: string;
  defaultAssociationUnit?: string;
}

// Error types specific to credit cards
export interface CreditCardError {
  code: string;
  message: string;
  field?: keyof CreateCreditCardRequest;
  details?: Record<string, any>;
}

// Credit card brand detection
export enum CreditCardBrand {
  VISA = "visa",
  MASTERCARD = "mastercard",
  AMERICAN_EXPRESS = "american-express",
  DISCOVER = "discover",
  DINERS = "diners",
  JCB = "jcb",
  UNKNOWN = "unknown"
}

// Utility types for credit card operations
export interface CreditCardValidationResult {
  isValid: boolean;
  errors: CreditCardError[];
  brand?: CreditCardBrand;
}

export interface CreditCardFormState {
  data: Partial<CreateCreditCardFormData>;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isValid: boolean;
}

// API operation types
export type CreditCardOperation = 'create' | 'read' | 'list';

export interface CreditCardApiConfig {
  baseUrl: string;
  endpoints: {
    list: string;
    create: string;
    getById: string;
  };
}
