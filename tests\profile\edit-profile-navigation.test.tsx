import React from "react";
import {render, fireEvent} from "@testing-library/react-native";
import EditProfile from "@/app/(profile)/edit-profile";
import {useRouter} from "expo-router";

// Mock do expo-router
jest.mock("expo-router", () => ({
  useRouter: jest.fn()
}));

// Mock dos hooks de API
jest.mock("@/hooks/api/use-users", () => ({
  useCurrentUser: () => ({
    data: {
      id: "1",
      name: "Test User",
      email: "<EMAIL>",
      document: "12345678901",
      phone: "+5511999999999",
      birthDate: "1990-01-01"
    },
    isLoading: false
  }),
  useUpdateProfile: () => ({
    mutate: jest.fn(),
    isPending: false,
    error: null
  }),
  useChangePassword: () => ({
    mutate: jest.fn(),
    isPending: false,
    error: null
  })
}));

// Mock do contexto de modal
jest.mock("@/contexts/bottom-modal-context", () => ({
  useBottomModal: () => ({
    openModal: jest.fn(),
    closeModal: jest.fn()
  })
}));

describe("EditProfile Navigation", () => {
  const mockBack = jest.fn();
  const mockRouter = {
    back: mockBack,
    push: jest.fn(),
    replace: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  it("should call router.back() when 'Voltar para perfil' button is pressed", () => {
    const {getByText} = render(<EditProfile />);
    
    const backButton = getByText("Voltar para perfil");
    fireEvent.press(backButton);
    
    expect(mockBack).toHaveBeenCalledTimes(1);
  });

  it("should call router.back() when 'Voltar' button is pressed in password tab", () => {
    const {getByText} = render(<EditProfile />);
    
    // Mudar para a aba de senha
    const passwordTab = getByText("Alterar senha");
    fireEvent.press(passwordTab);
    
    // Pressionar o botão Voltar
    const backButton = getByText("Voltar");
    fireEvent.press(backButton);
    
    expect(mockBack).toHaveBeenCalledTimes(1);
  });
});
