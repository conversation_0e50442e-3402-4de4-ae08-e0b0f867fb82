/**
 * Modelos de dados para pagamentos do ClubM
 * Baseado na análise da API Swagger e padrões do projeto
 */

import {z} from "zod";
import {PaginationRequest, Address} from "./common.models";

// Enums baseados na API real do ClubM
export enum PaymentStatus {
  Created = 0,
  Pending = 1,
  Confirmed = 2,
  Failed = 3,
  Unknown = 4
}

export enum PaymentEntity {
  Product = 0,
  Event = 1,
  Opportunity = 2
}

export enum PaymentType {
  CreditCard = 0,
  Boleto = 1,
  Pix = 2,
  Free = 3
}

export enum OrderStatus {
  DRAFT = "draft",
  PENDING_PAYMENT = "pending_payment",
  PAID = "paid",
  PROCESSING = "processing",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
  REFUNDED = "refunded"
}

export enum CartItemType {
  EVENT = "event",
  PRODUCT = "product",
  SERVICE = "service"
}

export enum PaymentMethodType {
  CREDIT_CARD = "credit_card",
  DEBIT_CARD = "debit_card",
  PIX = "pix",
  BOLETO = "boleto",
  BANK_TRANSFER = "bank_transfer",
  DIGITAL_WALLET = "digital_wallet"
}

export interface PaymentMethod {
  id: string;
  type: PaymentMethodType;
  name: string;
  description?: string;
  isActive: boolean;
  processingTime?: string;
  fees?: {
    percentage?: number;
    fixed?: number;
  };
  installments?: {
    min: number;
    max: number;
    feePercentage?: number;
  };
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Interface baseada no schema exato da API ClubM - CreateCreditCardViewModel
export interface CreateCreditCardRequest {
  // Dados do cartão
  holderName: string;
  number: string;
  expiryMonth: string; // API usa string, não number
  expiryYear: string; // API usa string, não number
  ccv: string; // API usa "ccv", não "cvv"

  // Dados pessoais obrigatórios
  name: string; // Nome completo do titular
  cpfCnpj: string; // CPF ou CNPJ
  phoneNumber: string; // Telefone

  // Dados de endereço obrigatórios
  postalCode: string; // CEP
  addressNumber: string; // Número do endereço
  addressComplement: string; // Complemento do endereço
}

export interface CreditCard {
  id: string;
  number?: string; // Campo da API que contém os últimos 4 dígitos (ex: "4444")
  holderName: string;
  expirationMonth: number;
  expirationYear: number;
  brand?: string | number; // Pode ser string ou número (0=Visa, 1=Mastercard, etc)
  lastFourDigits?: string; // Campo alternativo para últimos 4 dígitos
  isDefault?: boolean;
  userId?: number; // ID do usuário proprietário
  createdAt: string;
  updatedAt: string;
}

export interface CreatePaymentRequest {
  entity: PaymentEntity;
  entityId: number;
  type: PaymentType;
  creditCard?: {
    creditCardId: number;
    installmentCount: number;
  };
  boleto?: {
    installmentCount: number;
  };
}

export interface Payment {
  id: string;
  entity: PaymentEntity;
  entityId: number;
  type: PaymentType;
  value: number; // API retorna 'value', não 'amount'
  amount?: number; // Mantém para compatibilidade
  status: PaymentStatus;
  description?: string;
  userId: string | number; // API pode retornar number
  transactionId?: string;
  pixQrCode?: string;
  pixQrCodeBase64?: string;
  boletoUrl?: string;
  boletoBarcode?: string;
  expiresAt?: string;
  paidAt?: string;
  createdAt: string;
  // Campos adicionais da API
  installmentCount?: number;
  creditCardId?: number;
  termVersionAcceptanceId?: string | null;
  updatedAt: string;
  creditCard?: {
    creditCardId: number;
    installmentCount: number;
  };
  boleto?: {
    installmentCount: number;
  };
}

// Schemas de validação com Zod baseados na API real
export const CreateCreditCardSchema = z.object({
  // Dados do cartão
  number: z
    .string()
    .min(1, "Número do cartão é obrigatório")
    .refine((val) => {
      const cleaned = val.replace(/\D/g, "");
      return cleaned.length >= 13 && cleaned.length <= 19;
    }, "Número do cartão deve ter entre 13 e 19 dígitos")
    .refine((val) => {
      // Validação básica de Luhn (algoritmo de cartão de crédito)
      const cleaned = val.replace(/\D/g, "");
      if (cleaned.length < 13) return false;

      // Números de teste válidos para desenvolvimento
      const testCards = [
        "****************", // Visa test
        "****************", // Visa test
        "****************", // Mastercard test
        "****************", // Mastercard test
        "***************", // American Express test
        "***************", // American Express test
        "****************", // Discover test
        "****************" // Discover test
      ];

      if (testCards.includes(cleaned)) {
        console.log(
          "✅ [VALIDATION] Número de cartão de teste válido:",
          cleaned
        );
        return true;
      }

      let sum = 0;
      let isEven = false;

      for (let i = cleaned.length - 1; i >= 0; i--) {
        let digit = parseInt(cleaned[i]);

        if (isEven) {
          digit *= 2;
          if (digit > 9) {
            digit -= 9;
          }
        }

        sum += digit;
        isEven = !isEven;
      }

      const isValid = sum % 10 === 0;
      console.log("🔍 [VALIDATION] Validação Luhn:", {
        numero: cleaned,
        soma: sum,
        valido: isValid
      });

      return isValid;
    }, "Número do cartão inválido"),

  holderName: z
    .string()
    .min(1, "Nome do portador é obrigatório")
    .max(100, "Nome do portador deve ter no máximo 100 caracteres")
    .refine((val) => val.trim().length > 0, "Nome do portador é obrigatório")
    .refine(
      (val) => /^[a-zA-ZÀ-ÿ\s]+$/.test(val.trim()),
      "Nome deve conter apenas letras e espaços"
    ),

  // API usa strings para mês/ano, não numbers
  expiryMonth: z
    .string()
    .min(1, "Mês é obrigatório")
    .refine((val) => {
      const month = parseInt(val);
      return month >= 1 && month <= 12;
    }, "Mês deve ser entre 01 e 12"),

  expiryYear: z
    .string()
    .min(1, "Ano é obrigatório")
    .refine((val) => {
      const year = parseInt(val);
      const currentYear = new Date().getFullYear();
      return year >= currentYear && year <= currentYear + 20;
    }, "Ano inválido"),

  // API usa "ccv", não "cvv"
  ccv: z
    .string()
    .min(1, "CCV é obrigatório")
    .refine((val) => {
      const cleaned = val.replace(/\D/g, "");
      return cleaned.length >= 3 && cleaned.length <= 4;
    }, "CCV deve ter 3 ou 4 dígitos"),

  // Dados pessoais obrigatórios
  name: z
    .string()
    .min(1, "Nome completo é obrigatório")
    .max(200, "Nome deve ter no máximo 200 caracteres"),

  cpfCnpj: z
    .string()
    .min(1, "CPF/CNPJ é obrigatório")
    .refine((val) => {
      const cleaned = val.replace(/\D/g, "");
      return cleaned.length === 11 || cleaned.length === 14;
    }, "CPF deve ter 11 dígitos ou CNPJ deve ter 14 dígitos"),

  phoneNumber: z
    .string()
    .min(1, "Telefone é obrigatório")
    .refine((val) => {
      const cleaned = val.replace(/\D/g, "");
      return cleaned.length >= 10 && cleaned.length <= 11;
    }, "Telefone deve ter 10 ou 11 dígitos"),

  // Dados de endereço obrigatórios
  postalCode: z
    .string()
    .min(1, "CEP é obrigatório")
    .refine((val) => {
      const cleaned = val.replace(/\D/g, "");
      return cleaned.length === 8;
    }, "CEP deve ter 8 dígitos"),

  addressNumber: z
    .string()
    .min(1, "Número do endereço é obrigatório")
    .max(20, "Número deve ter no máximo 20 caracteres"),

  addressComplement: z
    .string()
    .max(100, "Complemento deve ter no máximo 100 caracteres")
    .optional()
    .default("")
});

export const CreatePaymentSchema = z.object({
  entity: z.nativeEnum(PaymentEntity),
  entityId: z.number().positive(),
  type: z.nativeEnum(PaymentType),
  creditCard: z
    .object({
      creditCardId: z.number().positive(),
      installmentCount: z.number().min(1).max(12)
    })
    .optional(),
  boleto: z
    .object({
      installmentCount: z.number().min(1).max(12)
    })
    .optional()
});

// Tipos derivados
export type CreateCreditCardFormData = z.infer<typeof CreateCreditCardSchema>;

// Interface para configuração de métodos de pagamento
export interface PaymentMethodConfig {
  type: PaymentMethodType;
  name: string;
  description: string;
  icon?: string;
  isActive: boolean;
  processingTime?: string;
  fees?: {
    percentage?: number;
    fixed?: number;
  };
  installments?: {
    min: number;
    max: number;
    feePercentage?: number;
  };
  metadata?: Record<string, any>;
}

export interface CartItem {
  id: string;
  type: CartItemType;
  referenceId: string; // ID do evento/produto/serviço
  name: string;
  description?: string;
  price: number;
  quantity: number;
  imageUrl?: string;
  metadata?: Record<string, any>;
  addedAt: string;
}

export interface OrderItem {
  id: string;
  type: CartItemType;
  referenceId: string;
  name: string;
  description?: string;
  unitPrice: number;
  quantity: number;
  totalPrice: number;
  metadata?: Record<string, any>;
}

export interface OrderSummary {
  subtotal: number;
  discounts: number;
  fees: number;
  taxes: number;
  total: number;
  currency: string;
}

export interface Order {
  id: string;
  userId: string;
  status: OrderStatus;
  items: OrderItem[];
  summary: OrderSummary;
  paymentMethodId?: string;
  paymentMethod?: PaymentMethod;
  shippingAddress?: Address;
  billingAddress?: Address;
  notes?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
}

export interface PaymentTransaction {
  id: string;
  orderId: string;
  paymentMethodId: string;
  paymentMethod: PaymentMethod;
  amount: number;
  currency: string;
  status: PaymentStatus;
  externalTransactionId?: string;
  paymentCode?: string; // Código PIX ou linha digitável do boleto
  qrCode?: string; // QR Code para PIX
  boletoUrl?: string; // URL do PDF do boleto
  dueDate?: string; // Data de vencimento
  paidAt?: string;
  failureReason?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Tipos de requisição
export interface CreateOrderRequest {
  items: Array<{
    type: CartItemType;
    referenceId: string;
    quantity: number;
  }>;
  paymentMethodId?: string;
  shippingAddress?: Omit<Address, "id">;
  billingAddress?: Omit<Address, "id">;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface ProcessPaymentRequest {
  orderId: string;
  paymentMethodId: string;
  installments?: number;
  cardData?: {
    holderName: string;
    number: string;
    expiryMonth: string;
    expiryYear: string;
    cvv: string;
  };
  metadata?: Record<string, any>;
}

export interface PaymentMethodsListParams extends PaginationRequest {
  type?: PaymentMethodType;
  isActive?: boolean;
}

export interface OrdersListParams extends PaginationRequest {
  status?: OrderStatus;
  startDate?: string;
  endDate?: string;
  userId?: string;
}

export interface PaymentTransactionsListParams extends PaginationRequest {
  orderId?: string;
  status?: PaymentStatus;
  paymentMethodType?: PaymentMethodType;
  startDate?: string;
  endDate?: string;
}

// Tipos de resposta
export interface CreateOrderResponse {
  order: Order;
  paymentMethods: PaymentMethod[];
}

export interface ProcessPaymentResponse {
  transaction: PaymentTransaction;
  order: Order;
  redirectUrl?: string;
  instructions?: string[];
}

export interface PaymentStatusResponse {
  transaction: PaymentTransaction;
  order: Order;
}

// Schemas de validação com Zod
export const CreateOrderRequestSchema = z.object({
  items: z
    .array(
      z.object({
        type: z.nativeEnum(CartItemType),
        referenceId: z.string().min(1),
        quantity: z.number().min(1)
      })
    )
    .min(1),
  paymentMethodId: z.string().optional(),
  shippingAddress: z
    .object({
      street: z.string().min(1),
      number: z.string().min(1),
      complement: z.string().optional(),
      neighborhood: z.string().min(1),
      city: z.string().min(1),
      state: z.string().min(2).max(2),
      zipCode: z.string().min(8).max(9),
      country: z.string().default("BR")
    })
    .optional(),
  billingAddress: z
    .object({
      street: z.string().min(1),
      number: z.string().min(1),
      complement: z.string().optional(),
      neighborhood: z.string().min(1),
      city: z.string().min(1),
      state: z.string().min(2).max(2),
      zipCode: z.string().min(8).max(9),
      country: z.string().default("BR")
    })
    .optional(),
  notes: z.string().optional(),
  metadata: z.record(z.any()).optional()
});

export const ProcessPaymentRequestSchema = z.object({
  orderId: z.string().min(1),
  paymentMethodId: z.string().min(1),
  installments: z.number().min(1).max(12).optional(),
  cardData: z
    .object({
      holderName: z.string().min(1),
      number: z.string().min(13).max(19),
      expiryMonth: z.string().length(2),
      expiryYear: z.string().length(4),
      cvv: z.string().min(3).max(4)
    })
    .optional(),
  metadata: z.record(z.any()).optional()
});

export interface UsePaymentOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
  retryAttempts?: number;
}
