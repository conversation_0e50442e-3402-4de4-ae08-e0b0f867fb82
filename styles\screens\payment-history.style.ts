/**
 * Estilos para a tela de histórico de pagamentos
 * Seguindo os padrões de design do projeto
 */

import { StyleSheet } from 'react-native';
import stylesConstants from '../styles-constants';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },

  // Filtros
  filtersContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: stylesConstants.colors.mainBackground
  },

  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },

  filterButtonActive: {
    backgroundColor: stylesConstants.colors.brand.brand500,
    borderColor: stylesConstants.colors.brand.brand500
  },

  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.inter
  },

  filterButtonTextActive: {
    color: stylesConstants.colors.white
  },

  // Lista de pagamentos
  paymentsList: {
    flex: 1
  },

  paymentsListContent: {
    padding: 20,
    paddingTop: 0
  },

  paymentItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },

  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12
  },

  paymentTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },

  paymentTypeIcon: {
    width: 20,
    height: 20,
    marginRight: 8,
    color: stylesConstants.colors.textSecondary
  },

  paymentType: {
    fontSize: 14,
    fontWeight: '500',
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.inter
  },

  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },

  statusIconSuccess: {
    width: 16,
    height: 16,
    marginRight: 6,
    color: stylesConstants.colors.green400
  },

  statusIconPending: {
    width: 16,
    height: 16,
    marginRight: 6,
    color: stylesConstants.colors.alert400
  },

  statusIconError: {
    width: 16,
    height: 16,
    marginRight: 6,
    color: stylesConstants.colors.error500
  },

  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.inter
  },

  statusTextSuccess: {
    color: stylesConstants.colors.green400
  },

  statusTextError: {
    color: stylesConstants.colors.error500
  },

  paymentDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },

  paymentAmount: {
    fontSize: 18,
    fontWeight: '700',
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.inter
  },

  paymentDate: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.inter
  },

  paymentInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },

  paymentId: {
    fontSize: 12,
    color: stylesConstants.colors.textSecondary,
    fontFamily: 'monospace'
  },

  transactionId: {
    fontSize: 12,
    color: stylesConstants.colors.textSecondary,
    fontFamily: 'monospace'
  },

  // Estados vazios e de erro
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40
  },

  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: stylesConstants.colors.textPrimary,
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: stylesConstants.fonts.inter
  },

  emptyMessage: {
    fontSize: 16,
    color: stylesConstants.colors.textSecondary,
    textAlign: 'center',
    fontFamily: stylesConstants.fonts.inter
  },

  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40
  },

  errorIcon: {
    width: 48,
    height: 48,
    color: stylesConstants.colors.error500,
    marginBottom: 16
  },

  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: stylesConstants.colors.textPrimary,
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: stylesConstants.fonts.inter
  },

  errorMessage: {
    fontSize: 16,
    color: stylesConstants.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: stylesConstants.fonts.inter
  },

  retryButton: {
    backgroundColor: stylesConstants.colors.brand.brand500,
    paddingHorizontal: 32
  }
});

export default styles;
