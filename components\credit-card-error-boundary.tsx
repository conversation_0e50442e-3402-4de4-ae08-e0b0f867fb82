/**
 * Credit Card Error Boundary
 * Specialized error boundary for credit card operations with specific error handling
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { ErrorBoundary, withErrorBoundary } from './error-boundary';
import { BaseApiError, ValidationError, NetworkError, AuthenticationError } from '@/services/api/base/api-errors';
import { useApiErrorHandler } from '@/hooks/api/use-api-error-handler';

interface CreditCardErrorFallbackProps {
  error: Error;
  retry: () => void;
}

const CreditCardErrorFallback: React.FC<CreditCardErrorFallbackProps> = ({
  error,
  retry
}) => {
  const { t } = useTranslation();
  const { getErrorMessage } = useApiErrorHandler();

  const getSpecificErrorMessage = (error: Error): string => {
    if (error instanceof ValidationError) {
      return t('creditCard.errors.validation', 'Dados do cartão inválidos. Verifique as informações e tente novamente.');
    }
    
    if (error instanceof NetworkError) {
      return t('creditCard.errors.network', 'Erro de conexão. Verifique sua internet e tente novamente.');
    }
    
    if (error instanceof AuthenticationError) {
      return t('creditCard.errors.auth', 'Sessão expirada. Faça login novamente.');
    }
    
    if (error instanceof BaseApiError) {
      switch (error.code) {
        case 'CARD_ALREADY_EXISTS':
          return t('creditCard.errors.cardExists', 'Este cartão já está cadastrado.');
        case 'INVALID_CARD_NUMBER':
          return t('creditCard.errors.invalidNumber', 'Número do cartão inválido.');
        case 'EXPIRED_CARD':
          return t('creditCard.errors.expired', 'Cartão expirado.');
        case 'INVALID_CVV':
          return t('creditCard.errors.invalidCvv', 'CVV inválido.');
        default:
          return getErrorMessage(error);
      }
    }
    
    return t('creditCard.errors.generic', 'Erro ao processar cartão. Tente novamente.');
  };

  const errorMessage = getSpecificErrorMessage(error);
  const isNetworkError = error instanceof NetworkError;
  const isValidationError = error instanceof ValidationError;

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>
          {isNetworkError 
            ? t('creditCard.errors.networkTitle', 'Problema de Conexão')
            : isValidationError
            ? t('creditCard.errors.validationTitle', 'Dados Inválidos')
            : t('creditCard.errors.title', 'Erro no Cartão')
          }
        </Text>
        
        <Text style={styles.message}>{errorMessage}</Text>
        
        {isNetworkError && (
          <Text style={styles.suggestion}>
            {t('creditCard.errors.networkSuggestion', 'Verifique sua conexão com a internet e tente novamente.')}
          </Text>
        )}
        
        {isValidationError && (
          <Text style={styles.suggestion}>
            {t('creditCard.errors.validationSuggestion', 'Verifique os dados do cartão e corrija os campos destacados.')}
          </Text>
        )}
        
        <TouchableOpacity style={styles.retryButton} onPress={retry}>
          <Text style={styles.retryButtonText}>
            {t('common.tryAgain', 'Tentar Novamente')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

interface CreditCardErrorBoundaryProps {
  children: React.ReactNode;
  onError?: (error: Error) => void;
}

export const CreditCardErrorBoundary: React.FC<CreditCardErrorBoundaryProps> = ({
  children,
  onError
}) => {
  return (
    <ErrorBoundary
      fallback={(error, retry) => (
        <CreditCardErrorFallback error={error} retry={retry} />
      )}
      onError={(error, errorInfo) => {
        onError?.(error);
        // Additional credit card specific error logging
        console.error('Credit Card Error:', {
          error: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          timestamp: new Date().toISOString()
        });
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

/**
 * HOC to wrap credit card components with error boundary
 */
export const withCreditCardErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  onError?: (error: Error) => void
) => {
  const WrappedComponent = (props: P) => (
    <CreditCardErrorBoundary onError={onError}>
      <Component {...props} />
    </CreditCardErrorBoundary>
  );

  WrappedComponent.displayName = `withCreditCardErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8f9fa'
  },
  content: {
    backgroundColor: 'white',
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    maxWidth: 350,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
    textAlign: 'center'
  },
  message: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20
  },
  suggestion: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 16,
    fontStyle: 'italic'
  },
  retryButton: {
    backgroundColor: '#0f7c4d',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120
  },
  retryButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center'
  }
});

export default CreditCardErrorBoundary;
