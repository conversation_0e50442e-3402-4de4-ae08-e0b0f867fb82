/**
 * Credit Cards API Service
 * Handles all credit card related API operations
 */

import { firstValueFrom } from 'rxjs';
import { apiClient } from '../base/api-client';
import { ApiLogger } from '../base/api-logger';
import {
  CreditCard,
  CreateCreditCardRequest,
  CreditCardsResponse,
  CreditCardsListParams
} from '@/models/api/credit-cards.models';

export class CreditCardsService {
  private static readonly BASE_PATH = '/api/app/creditCards';

  /**
   * Get paginated list of credit cards for the authenticated user
   */
  static async getCreditCards(
    params?: CreditCardsListParams
  ): Promise<CreditCardsResponse> {
    try {
      ApiLogger.info('Buscando cartões de crédito', params);

      const response = await firstValueFrom(
        apiClient.get<CreditCardsResponse>(this.BASE_PATH, params)
      );

      ApiLogger.info(`Encontrados ${response.data.length} cartões de crédito`);
      return response;
    } catch (error) {
      ApiLogger.error('Erro ao buscar cartões de crédito', error as Error);
      throw error;
    }
  }

  /**
   * Get a specific credit card by ID
   */
  static async getCreditCard(id: number): Promise<CreditCard> {
    try {
      ApiLogger.info('Buscando cartão de crédito por ID', { id });

      const response = await firstValueFrom(
        apiClient.get<CreditCard>(`${this.BASE_PATH}/${id}`)
      );

      ApiLogger.info('Cartão de crédito encontrado', { id, holderName: response.holderName });
      return response;
    } catch (error) {
      ApiLogger.error('Erro ao buscar cartão de crédito', error as Error, { id });
      throw error;
    }
  }

  /**
   * Create a new credit card
   */
  static async createCreditCard(
    creditCardData: CreateCreditCardRequest
  ): Promise<CreditCard> {
    try {
      ApiLogger.info('Criando novo cartão de crédito', {
        holderName: creditCardData.holderName,
        lastFourDigits: creditCardData.number.slice(-4)
      });

      const response = await firstValueFrom(
        apiClient.post<CreditCard>(this.BASE_PATH, creditCardData)
      );

      ApiLogger.info('Cartão de crédito criado com sucesso', {
        id: response.id,
        holderName: response.holderName,
        lastFourDigits: response.lastFourDigits
      });

      return response;
    } catch (error) {
      ApiLogger.error('Erro ao criar cartão de crédito', error as Error, {
        holderName: creditCardData.holderName
      });
      throw error;
    }
  }

  /**
   * Validate credit card data before submission
   */
  static validateCreditCardData(data: CreateCreditCardRequest): string[] {
    const errors: string[] = [];

    // Validate holder name
    if (!data.holderName || data.holderName.trim().length < 2) {
      errors.push('Nome do portador deve ter pelo menos 2 caracteres');
    }

    // Validate card number
    if (!data.number || !/^\d{13,19}$/.test(data.number.replace(/\s/g, ''))) {
      errors.push('Número do cartão deve ter entre 13 e 19 dígitos');
    }

    // Validate expiry month
    if (!data.expiryMonth || data.expiryMonth < 1 || data.expiryMonth > 12) {
      errors.push('Mês de expiração deve ser entre 1 e 12');
    }

    // Validate expiry year
    const currentYear = new Date().getFullYear();
    if (!data.expiryYear || data.expiryYear < currentYear) {
      errors.push('Ano de expiração não pode ser no passado');
    }

    // Validate CVV
    if (!data.cvv || !/^\d{3,4}$/.test(data.cvv)) {
      errors.push('CVV deve ter 3 ou 4 dígitos');
    }

    // Validate expiry date is not in the past
    if (data.expiryYear && data.expiryMonth) {
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYearValue = currentDate.getFullYear();

      if (
        data.expiryYear === currentYearValue &&
        data.expiryMonth < currentMonth
      ) {
        errors.push('Data de expiração não pode ser no passado');
      }
    }

    return errors;
  }

  /**
   * Format credit card number for display
   */
  static formatCardNumber(cardNumber: string): string {
    const cleanNumber = cardNumber.replace(/\D/g, '');
    return cleanNumber.replace(/(.{4})/g, '$1 ').trim();
  }

  /**
   * Mask credit card number for security
   */
  static maskCardNumber(cardNumber: string): string {
    const cleanNumber = cardNumber.replace(/\D/g, '');
    if (cleanNumber.length < 4) return cardNumber;

    const lastFour = cleanNumber.slice(-4);
    const masked = '*'.repeat(cleanNumber.length - 4);
    return this.formatCardNumber(masked + lastFour);
  }

  /**
   * Detect credit card brand from number
   */
  static detectCardBrand(cardNumber: string): string {
    const cleanNumber = cardNumber.replace(/\D/g, '');

    // Visa
    if (/^4/.test(cleanNumber)) {
      return 'visa';
    }

    // Mastercard
    if (/^5[1-5]/.test(cleanNumber) || /^2(22[1-9]|2[3-9]|[3-6]|7[0-1]|720)/.test(cleanNumber)) {
      return 'mastercard';
    }

    // American Express
    if (/^3[47]/.test(cleanNumber)) {
      return 'american-express';
    }

    // Discover
    if (/^6/.test(cleanNumber)) {
      return 'discover';
    }

    // Diners
    if (/^3[068]/.test(cleanNumber)) {
      return 'diners';
    }

    // JCB
    if (/^35/.test(cleanNumber)) {
      return 'jcb';
    }

    return 'unknown';
  }

  /**
   * Validate card number using Luhn algorithm
   */
  static validateCardNumber(cardNumber: string): boolean {
    const cleanNumber = cardNumber.replace(/\D/g, '');

    if (cleanNumber.length < 13 || cleanNumber.length > 19) {
      return false;
    }

    let sum = 0;
    let isEven = false;

    // Loop through values starting from the right
    for (let i = cleanNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cleanNumber.charAt(i), 10);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }
}

export default CreditCardsService;
