{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-66:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,211,282,352,435,502,569,648,727,815,908,976,1062,1147,1223,1306,1388,1463,1541,1615,1701,1773,1852,1928", "endColumns": "69,85,70,69,82,66,66,78,78,87,92,67,85,84,75,82,81,74,77,73,85,71,78,75,85", "endOffsets": "120,206,277,347,430,497,564,643,722,810,903,971,1057,1142,1218,1301,1383,1458,1536,1610,1696,1768,1847,1923,2009"}, "to": {"startLines": "29,39,61,62,63,64,65,66,67,68,69,70,72,74,75,76,77,78,79,80,81,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2843,3913,6600,6671,6741,6824,6891,6958,7037,7116,7204,7297,7493,7665,7750,7826,7909,7991,8066,8144,8218,8405,8477,8556,8632", "endColumns": "69,85,70,69,82,66,66,78,78,87,92,67,85,84,75,82,81,74,77,73,85,71,78,75,85", "endOffsets": "2908,3994,6666,6736,6819,6886,6953,7032,7111,7199,7292,7360,7574,7745,7821,7904,7986,8061,8139,8213,8299,8472,8551,8627,8713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,206,320,424,518,662,769,890,1007,1146,1293,1424,1554,1698,1799,1962,2088,2222,2366,2489,2617,2710,2838,2922,3057,3160,3298", "endColumns": "150,113,103,93,143,106,120,116,138,146,130,129,143,100,162,125,133,143,122,127,92,127,83,134,102,137,101", "endOffsets": "201,315,419,513,657,764,885,1002,1141,1288,1419,1549,1693,1794,1957,2083,2217,2361,2484,2612,2705,2833,2917,3052,3155,3293,3395"}, "to": {"startLines": "30,31,40,42,43,44,48,49,50,51,52,53,54,55,56,57,58,59,60,71,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2913,3064,3999,4218,4312,4456,4880,5001,5118,5257,5404,5535,5665,5809,5910,6073,6199,6333,6477,7365,8718,8811,8939,9023,9158,9261,9399", "endColumns": "150,113,103,93,143,106,120,116,138,146,130,129,143,100,162,125,133,143,122,127,92,127,83,134,102,137,101", "endOffsets": "3059,3173,4098,4307,4451,4558,4996,5113,5252,5399,5530,5660,5804,5905,6068,6194,6328,6472,6595,7488,8806,8934,9018,9153,9256,9394,9496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "32,33,34,35,36,37,38,82", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3178,3275,3377,3476,3576,3683,3793,8304", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3270,3372,3471,3571,3678,3788,3908,8400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,7579", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,7660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "41,45,46,47", "startColumns": "4,4,4,4", "startOffsets": "4103,4563,4662,4774", "endColumns": "114,98,111,105", "endOffsets": "4213,4657,4769,4875"}}]}]}