/**
 * SignalR Chat Hook
 * Provides real-time messaging functionality using SignalR
 */

import {useEffect, useRef, useState, useCallback} from "react";
import {useQueryClient} from "@tanstack/react-query";
import {
  SignalRChatService,
  SignalREventHandlers
} from "@/services/api/chats/signalr-chat.service";
import {chatsKeys} from "./use-chats";
import {
  SafeChatMessageViewModel,
  SignalRConnectionState,
  TypingUser,
  SignalRChatEvents
} from "@/models/api/chats-api.models";
import {ApiLogger} from "@/services/api/base/api-logger";

export interface UseSignalRChatOptions {
  hubUrl: string;
  accessTokenFactory: () => string | Promise<string>;
  enabled?: boolean;
  autoConnect?: boolean;
  logLevel?: any; // signalR.LogLevel
}

export interface UseSignalRChatReturn {
  // Connection state
  connectionState: SignalRConnectionState;
  isConnected: boolean;
  isConnecting: boolean;
  isReconnecting: boolean;

  // Connection methods
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;

  // Chat methods
  joinChat: (chatId: number) => Promise<void>;
  leaveChat: (chatId: number) => Promise<void>;
  sendMessage: (chatId: number, message: string) => Promise<void>;

  // Typing indicators
  sendTypingIndicator: (chatId: number, isTyping: boolean) => Promise<void>;
  typingUsers: TypingUser[];

  // Message status
  markMessageAsRead: (messageId: number) => Promise<void>;

  // Online users
  onlineUsers: {userId: number; isOnline: boolean; lastSeen?: string}[];

  // Service instance (for advanced usage)
  service: SignalRChatService | null;
}

/**
 * Hook for SignalR real-time chat functionality
 */
export const useSignalRChat = ({
  hubUrl,
  accessTokenFactory,
  enabled = true,
  autoConnect = true,
  logLevel
}: UseSignalRChatOptions): UseSignalRChatReturn => {
  const queryClient = useQueryClient();
  const serviceRef = useRef<SignalRChatService | null>(null);

  // State
  const [connectionState, setConnectionState] =
    useState<SignalRConnectionState>({
      status: "disconnected",
      reconnectAttempts: 0,
      maxReconnectAttempts: 5
    });
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<
    {userId: number; isOnline: boolean; lastSeen?: string}[]
  >([]);

  // Derived state
  const isConnected = connectionState.status === "connected";
  const isConnecting = connectionState.status === "connecting";
  const isReconnecting = connectionState.status === "reconnecting";

  /**
   * Initialize SignalR service
   */
  const initializeService = useCallback(async () => {
    if (serviceRef.current || !enabled) return;

    try {
      ApiLogger.info("Initializing SignalR service");

      const service = new SignalRChatService({
        hubUrl,
        accessTokenFactory,
        automaticReconnect: true,
        logLevel
      });

      // Set up event handlers
      const eventHandlers: SignalREventHandlers = {
        onMessageReceived: (message: SafeChatMessageViewModel) => {
          ApiLogger.info("Real-time message received", {
            messageId: message.id,
            chatId: message.chatId
          });

          // Invalidate messages query to refresh the UI
          queryClient.invalidateQueries({
            queryKey: chatsKeys.messages(message.chatId.toString())
          });

          // Also invalidate chats list to update last message
          queryClient.invalidateQueries({
            queryKey: chatsKeys.list()
          });
        },

        onTypingIndicator: (data: SignalRChatEvents["TypingIndicator"]) => {
          setTypingUsers((prev) => {
            const filtered = prev.filter((user) => user.userId !== data.userId);

            if (data.isTyping) {
              return [
                ...filtered,
                {
                  userId: data.userId,
                  userName: data.userName,
                  startedAt: new Date()
                }
              ];
            }

            return filtered;
          });
        },

        onMessageStatusUpdate: (
          data: SignalRChatEvents["MessageStatusUpdate"]
        ) => {
          // Update message status in cache if needed
          ApiLogger.info("Message status updated", data);
        },

        onUserJoined: (data: SignalRChatEvents["UserJoined"]) => {
          ApiLogger.info("User joined chat", data);

          // Update online users
          setOnlineUsers((prev) => {
            const filtered = prev.filter((user) => user.userId !== data.userId);
            return [...filtered, {userId: data.userId, isOnline: true}];
          });
        },

        onUserLeft: (data: SignalRChatEvents["UserLeft"]) => {
          ApiLogger.info("User left chat", data);

          // Update online users
          setOnlineUsers((prev) =>
            prev.map((user) =>
              user.userId === data.userId
                ? {...user, isOnline: false, lastSeen: data.leftAt}
                : user
            )
          );
        },

        onUserOnlineStatusChanged: (
          data: SignalRChatEvents["UserOnlineStatusChanged"]
        ) => {
          setOnlineUsers((prev) => {
            const filtered = prev.filter((user) => user.userId !== data.userId);
            return [
              ...filtered,
              {
                userId: data.userId,
                isOnline: data.isOnline,
                lastSeen: data.lastSeen
              }
            ];
          });
        },

        onConnectionStateChanged: (state: SignalRConnectionState) => {
          setConnectionState(state);
        }
      };

      service.setEventHandlers(eventHandlers);
      await service.initialize();

      serviceRef.current = service;

      ApiLogger.info("SignalR service initialized successfully");
    } catch (error) {
      ApiLogger.error("Failed to initialize SignalR service", error as Error);
    }
  }, [hubUrl, accessTokenFactory, enabled, logLevel, queryClient]);

  /**
   * Connect to SignalR hub
   */
  const connect = useCallback(async () => {
    if (!serviceRef.current) {
      await initializeService();
    }

    if (serviceRef.current) {
      await serviceRef.current.connect();
    }
  }, [initializeService]);

  /**
   * Disconnect from SignalR hub
   */
  const disconnect = useCallback(async () => {
    if (serviceRef.current) {
      await serviceRef.current.disconnect();
    }
  }, []);

  /**
   * Join a chat room
   */
  const joinChat = useCallback(async (chatId: number) => {
    if (serviceRef.current) {
      await serviceRef.current.joinChat(chatId);
    }
  }, []);

  /**
   * Leave a chat room
   */
  const leaveChat = useCallback(async (chatId: number) => {
    if (serviceRef.current) {
      await serviceRef.current.leaveChat(chatId);
    }
  }, []);

  /**
   * Send a message
   */
  const sendMessage = useCallback(async (chatId: number, message: string) => {
    if (serviceRef.current) {
      await serviceRef.current.sendMessage(chatId, message);
    }
  }, []);

  /**
   * Send typing indicator
   */
  const sendTypingIndicator = useCallback(
    async (chatId: number, isTyping: boolean) => {
      if (serviceRef.current) {
        await serviceRef.current.sendTypingIndicator(chatId, isTyping);
      }
    },
    []
  );

  /**
   * Mark message as read
   */
  const markMessageAsRead = useCallback(async (messageId: number) => {
    if (serviceRef.current) {
      await serviceRef.current.markMessageAsRead(messageId);
    }
  }, []);

  /**
   * Auto-connect on mount if enabled
   */
  useEffect(() => {
    if (enabled && autoConnect && hubUrl) {
      initializeService().then(() => {
        if (serviceRef.current && !serviceRef.current.isConnected()) {
          connect().catch((error) => {
            ApiLogger.error("Auto-connect failed", error);
          });
        }
      });
    }
  }, [enabled, autoConnect, hubUrl]); // Removed initializeService and connect from dependencies to prevent loops

  /**
   * Cleanup typing indicators
   */
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      setTypingUsers((prev) =>
        prev.filter((user) => {
          const timeDiff = now.getTime() - user.startedAt.getTime();
          return timeDiff < 5000; // Remove typing indicators older than 5 seconds
        })
      );
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (serviceRef.current) {
        serviceRef.current.disconnect().catch((error) => {
          ApiLogger.error("Error during cleanup disconnect", error);
        });
      }
    };
  }, []);

  return {
    // Connection state
    connectionState,
    isConnected,
    isConnecting,
    isReconnecting,

    // Connection methods
    connect,
    disconnect,

    // Chat methods
    joinChat,
    leaveChat,
    sendMessage,

    // Typing indicators
    sendTypingIndicator,
    typingUsers,

    // Message status
    markMessageAsRead,

    // Online users
    onlineUsers,

    // Service instance
    service: serviceRef.current
  };
};

export default useSignalRChat;
