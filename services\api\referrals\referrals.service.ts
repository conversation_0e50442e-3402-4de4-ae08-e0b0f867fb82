/**
 * Referrals Service for ClubM
 * Handles all referral/indication related API operations
 */

import { firstValueFrom } from 'rxjs';
import { apiClient } from '../base/api-client';
import { ApiLogger } from '../base/api-logger';
import {
  CreateReferralRequest,
  Referral,
  ReferralsListParams,
  ReferralsResponse,
  ReferralStats,
  ContactSearchParams,
  ContactSearchResponse,
  BulkReferralRequest,
  BulkReferralResponse,
  ResendInvitationRequest
} from '@/models/api/referrals.models';

export class ReferralsService {
  private static readonly BASE_PATH = '/api/app/indications';

  /**
   * Get paginated list of referrals for the authenticated user
   */
  static async getReferrals(
    params?: ReferralsListParams
  ): Promise<ReferralsResponse> {
    try {
      ApiLogger.info('Buscando referrals', params);

      const response = await firstValueFrom(
        apiClient.get<ReferralsResponse>(this.BASE_PATH, params)
      );

      ApiLogger.info(`Encontrados ${response.data.length} referrals`);
      return response;
    } catch (error) {
      ApiLogger.error('Erro ao buscar referrals', error as Error);
      throw error;
    }
  }

  /**
   * Get a specific referral by ID
   */
  static async getReferral(id: number): Promise<Referral> {
    try {
      ApiLogger.info('Buscando referral por ID', { id });

      const response = await firstValueFrom(
        apiClient.get<Referral>(`${this.BASE_PATH}/${id}`)
      );

      ApiLogger.info('Referral encontrado', { id, referral: response });
      return response;
    } catch (error) {
      ApiLogger.error('Erro ao buscar referral', error as Error);
      throw error;
    }
  }

  /**
   * Create a new referral
   */
  static async createReferral(
    referralData: CreateReferralRequest
  ): Promise<Referral> {
    try {
      ApiLogger.info('Criando novo referral', referralData);

      const response = await firstValueFrom(
        apiClient.post<Referral>(this.BASE_PATH, referralData)
      );

      ApiLogger.info('Referral criado com sucesso', { referral: response });
      return response;
    } catch (error) {
      ApiLogger.error('Erro ao criar referral', error as Error);
      throw error;
    }
  }

  /**
   * Create multiple referrals at once
   */
  static async createBulkReferrals(
    bulkData: BulkReferralRequest
  ): Promise<BulkReferralResponse> {
    try {
      ApiLogger.info('Criando referrals em lote', { count: bulkData.contacts.length });

      const response = await firstValueFrom(
        apiClient.post<BulkReferralResponse>(`${this.BASE_PATH}/bulk`, bulkData)
      );

      ApiLogger.info('Referrals em lote criados', {
        successful: response.successful,
        failed: response.failed
      });
      return response;
    } catch (error) {
      ApiLogger.error('Erro ao criar referrals em lote', error as Error);
      throw error;
    }
  }

  /**
   * Resend invitation for a referral
   */
  static async resendInvitation(
    resendData: ResendInvitationRequest
  ): Promise<void> {
    try {
      ApiLogger.info('Reenviando convite', resendData);

      await firstValueFrom(
        apiClient.post<void>(`${this.BASE_PATH}/${resendData.referralId}/resend`, {
          message: resendData.message
        })
      );

      ApiLogger.info('Convite reenviado com sucesso', { referralId: resendData.referralId });
    } catch (error) {
      ApiLogger.error('Erro ao reenviar convite', error as Error);
      throw error;
    }
  }

  /**
   * Cancel a referral
   */
  static async cancelReferral(id: number): Promise<void> {
    try {
      ApiLogger.info('Cancelando referral', { id });

      await firstValueFrom(
        apiClient.delete<void>(`${this.BASE_PATH}/${id}`)
      );

      ApiLogger.info('Referral cancelado com sucesso', { id });
    } catch (error) {
      ApiLogger.error('Erro ao cancelar referral', error as Error);
      throw error;
    }
  }

  /**
   * Get referral statistics for the authenticated user
   */
  static async getReferralStats(): Promise<ReferralStats> {
    try {
      ApiLogger.info('Buscando estatísticas de referrals');

      const response = await firstValueFrom(
        apiClient.get<ReferralStats>(`${this.BASE_PATH}/stats`)
      );

      ApiLogger.info('Estatísticas de referrals obtidas', response);
      return response;
    } catch (error) {
      ApiLogger.error('Erro ao buscar estatísticas de referrals', error as Error);
      throw error;
    }
  }

  /**
   * Search for contacts to invite
   */
  static async searchContacts(
    params: ContactSearchParams
  ): Promise<ContactSearchResponse> {
    try {
      ApiLogger.info('Buscando contatos para convite', params);

      const response = await firstValueFrom(
        apiClient.get<ContactSearchResponse>('/api/app/users', params)
      );

      ApiLogger.info(`Encontrados ${response.data.length} contatos`);
      return response;
    } catch (error) {
      ApiLogger.error('Erro ao buscar contatos', error as Error);
      throw error;
    }
  }

  /**
   * Get user by document (for contact validation)
   */
  static async getUserByDocument(document: string): Promise<any> {
    try {
      ApiLogger.info('Buscando usuário por documento', { document });

      const response = await firstValueFrom(
        apiClient.get<any>(`/api/app/users/${document}`)
      );

      ApiLogger.info('Usuário encontrado por documento', { document, user: response });
      return response;
    } catch (error) {
      ApiLogger.error('Erro ao buscar usuário por documento', error as Error);
      throw error;
    }
  }
}
