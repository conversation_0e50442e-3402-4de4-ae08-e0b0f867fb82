/**
 * Configuração principal do MSW (Mock Service Worker)
 * Centraliza todos os handlers de mock para desenvolvimento
 */

import {setupServer} from "msw/node";
import {paymentsHandlers} from "./payments.mock";

// Combina todos os handlers
const handlers = [
  ...paymentsHandlers
];

// Configura o servidor MSW
export const server = setupServer(...handlers);

// Configuração para React Native
export const mockHandlers = handlers;

// Função para inicializar mocks em desenvolvimento
export const initializeMocks = () => {
  if (__DEV__) {
    console.log("🔧 [MSW] Inicializando mocks para desenvolvimento");
    
    // Para React Native, usamos setupServer apenas em testes
    // Em desenvolvimento, os mocks são configurados via metro
    return true;
  }
  return false;
};

// Função para resetar mocks
export const resetMocks = () => {
  if (__DEV__) {
    console.log("🔄 [MSW] Resetando mocks");
    server.resetHandlers();
  }
};

export default {
  handlers,
  server,
  initializeMocks,
  resetMocks
};
