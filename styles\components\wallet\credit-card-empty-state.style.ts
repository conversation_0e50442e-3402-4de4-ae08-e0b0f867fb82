import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 52,
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    marginTop: 18
  },
  iconContainer: {
    marginBottom: 16,
    opacity: 0.8
  },
  title: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    color: stylesConstants.colors.textPrimary,
    textAlign: "center",
    marginBottom: 4
  },
  description: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    color: stylesConstants.colors.textPrimary,
    textAlign: "center",
    marginBottom: 20,
    paddingHorizontal: 8
  },
  button: {
    backgroundColor: stylesConstants.colors.brand.brand400,
    borderRadius: 8,
    paddingHorizontal: 61,
    paddingVertical: 10,
    minHeight: 44,
    width: 295,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#101828",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: stylesConstants.colors.brand.brand400
  },
  buttonText: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    color: stylesConstants.colors.fullWhite,
    textAlign: "center"
  }
});

export default styles;
