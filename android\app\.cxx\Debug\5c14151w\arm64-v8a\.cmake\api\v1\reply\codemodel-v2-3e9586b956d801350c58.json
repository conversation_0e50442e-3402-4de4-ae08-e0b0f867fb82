{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-d9d73f09c28b5a2a528a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "pagerview_autolinked_build", "jsonFile": "directory-pagerview_autolinked_build-Debug-bd0bbd4fda2521c5e286.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-e5c81d494a5e58b9ea14.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-d9f75cf71e2491b6ba80.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [6]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-88048baf0ab6618be184.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [4]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-45190ace0c2475d489f4.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [5]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-587ce17c44a84862a172.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_pagerview::@7032a8921530ec438d60", "jsonFile": "target-react_codegen_pagerview-Debug-65504f3c0fd6eeaa0ab3.json", "name": "react_codegen_pagerview", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-e7f8bf868525f1034567.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-daf4229e49ef0344d45f.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-804a4daf5ce9260ff2b7.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-607d1554e37eaaf77800.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-8f93d5f62e7eacd8d115.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/eclipse-workspace/club-m-app/android/app/.cxx/Debug/5c14151w/arm64-v8a", "source": "C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}