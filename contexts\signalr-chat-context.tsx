/**
 * SignalR Chat Context Provider
 * Provides SignalR chat functionality throughout the app
 */

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useMemo
} from "react";
import {
  useSignalRChat,
  UseSignalRChatReturn
} from "@/hooks/api/use-signalr-chat";
import {apiClient} from "@/services/api/base/api-client";
import {ApiLogger} from "@/services/api/base/api-logger";
import * as signalR from "@microsoft/signalr";
import {useAuth} from "@/contexts/AuthContext";

interface SignalRChatContextType extends UseSignalRChatReturn {
  // Additional context-specific methods or state can be added here
}

const SignalRChatContext = createContext<SignalRChatContextType | null>(null);

export interface SignalRChatProviderProps {
  children: React.ReactNode;
  enabled?: boolean;
}

export const SignalRChatProvider: React.FC<SignalRChatProviderProps> = ({
  children,
  enabled = true
}) => {
  const [hubUrl, setHubUrl] = useState<string>("");
  const {isAuthenticated, isLoading: authLoading} = useAuth();

  // Initialize hub URL
  useEffect(() => {
    const baseUrl = process.env.EXPO_PUBLIC_API_BASE_URL;
    if (baseUrl) {
      // Remove '/api' from the end if present and add the SignalR hub path
      const cleanBaseUrl = baseUrl.replace(/\/api$/, "");
      const signalRHubUrl = `${cleanBaseUrl}/hubs/chat`;
      setHubUrl(signalRHubUrl);

      ApiLogger.info("SignalR Hub URL configured", {hubUrl: signalRHubUrl});
    } else {
      ApiLogger.error("EXPO_PUBLIC_API_BASE_URL not configured");
    }
  }, []);

  // Access token factory function
  const accessTokenFactory = async (): Promise<string> => {
    try {
      // Check if user is authenticated first
      if (!isAuthenticated) {
        throw new Error("User is not authenticated");
      }

      // Aguardar até que o token esteja disponível (máximo 10 segundos)
      const maxAttempts = 20; // 20 tentativas x 500ms = 10 segundos
      let attempts = 0;

      while (attempts < maxAttempts) {
        const tokenData = await apiClient.getValidToken();
        if (tokenData?.accessToken && tokenData?.tokenType) {
          // Return the token with proper Bearer prefix format
          // SignalR expects the full authorization header value from accessTokenFactory
          const formattedToken = `${tokenData.tokenType} ${tokenData.accessToken}`;
          ApiLogger.info("SignalR token factory returning formatted token", {
            tokenType: tokenData.tokenType,
            hasAccessToken: !!tokenData.accessToken
          });
          return formattedToken;
        }

        // Aguardar 500ms antes da próxima tentativa
        await new Promise((resolve) => setTimeout(resolve, 500));
        attempts++;
      }

      throw new Error("No valid access token available after waiting");
    } catch (error) {
      ApiLogger.error("Failed to get access token for SignalR", error as Error);
      throw error;
    }
  };

  // Initialize SignalR chat hook
  // Only enable SignalR when user is authenticated and not loading
  const shouldEnableSignalR =
    enabled && !!hubUrl && isAuthenticated && !authLoading;

  const signalRChat = useSignalRChat({
    hubUrl,
    accessTokenFactory,
    enabled: shouldEnableSignalR,
    autoConnect: shouldEnableSignalR,
    logLevel: __DEV__ ? signalR.LogLevel.Debug : signalR.LogLevel.Warning
  });

  // Log SignalR enablement state
  useEffect(() => {
    ApiLogger.info("SignalR enablement state changed", {
      enabled,
      hasHubUrl: !!hubUrl,
      isAuthenticated,
      authLoading,
      shouldEnableSignalR
    });
  }, [enabled, hubUrl, isAuthenticated, authLoading, shouldEnableSignalR]);

  // Log connection state changes
  useEffect(() => {
    ApiLogger.info("SignalR connection state changed", {
      status: signalRChat.connectionState.status,
      isConnected: signalRChat.isConnected,
      reconnectAttempts: signalRChat.connectionState.reconnectAttempts
    });
  }, [signalRChat.connectionState, signalRChat.isConnected]);

  const contextValue: SignalRChatContextType = useMemo(
    () => ({
      ...signalRChat
    }),
    [signalRChat]
  );

  return (
    <SignalRChatContext.Provider value={contextValue}>
      {children}
    </SignalRChatContext.Provider>
  );
};

/**
 * Hook to use SignalR chat context
 */
export const useSignalRChatContext = (): SignalRChatContextType => {
  const context = useContext(SignalRChatContext);

  if (!context) {
    throw new Error(
      "useSignalRChatContext must be used within a SignalRChatProvider"
    );
  }

  return context;
};

/**
 * Hook to use SignalR chat context with optional fallback
 */
export const useSignalRChatContextOptional =
  (): SignalRChatContextType | null => {
    return useContext(SignalRChatContext);
  };

export default SignalRChatProvider;
