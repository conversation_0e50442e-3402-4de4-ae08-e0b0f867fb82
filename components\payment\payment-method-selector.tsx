/**
 * Componente para seleção de método de pagamento
 * Seguindo os padrões de design do projeto
 */

import React, {useCallback} from "react";
import {View, Text, TouchableOpacity, ScrollView} from "react-native";
import {useTranslation} from "react-i18next";
import {PaymentType, CreditCard} from "../../models/api/payments.models";
import styles from "../../styles/components/payment/payment-method-selector.style";
import CreditCardIcon from "../icons/credit-card-icon";
import PixIcon from "../icons/pix-icon";
import BoletoIcon from "../icons/boleto-icon";
import CheckIcon from "../icons/check-icon";

export interface PaymentMethodSelectorProps {
  selectedMethod?: PaymentType;
  onMethodSelect: (method: PaymentType, preserveSelectedCard?: boolean) => void;
  savedCreditCards?: CreditCard[];
  onSelectSavedCard?: (card: CreditCard) => void;
  selectedSavedCard?: CreditCard;
  showSavedCards?: boolean;
  onAddNewCard?: () => void;
  showNewCardForm?: boolean;
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  selectedMethod,
  onMethodSelect,
  savedCreditCards = [],
  onSelectSavedCard,
  selectedSavedCard,
  showSavedCards = true,
  onAddNewCard,
  showNewCardForm = false
}) => {
  const {t} = useTranslation();

  // Debug dos cartões recebidos
  console.log(
    "💳 [PAYMENT-METHOD-SELECTOR] 🔧 TESTE CARTÕES - Props recebidas:",
    {
      selectedMethod,
      savedCardsCount: savedCreditCards.length,
      showSavedCards,
      showNewCardForm,
      hasOnAddNewCard: !!onAddNewCard,
      savedCards: savedCreditCards.map((card) => ({
        id: card.id,
        lastFourDigits: card.lastFourDigits,
        brand: card.brand
      }))
    }
  );

  // Métodos de pagamento disponíveis
  const paymentMethods = [
    {
      type: PaymentType.Pix,
      title: t("components.payment.methods.pix.title"),
      description: t("components.payment.methods.pix.description"),
      icon: <PixIcon />,
      instantPayment: true
    },
    {
      type: PaymentType.CreditCard,
      title: t("components.payment.methods.creditCard.title"),
      description: t("components.payment.methods.creditCard.description"),
      icon: <CreditCardIcon />,
      instantPayment: true
    },
    {
      type: PaymentType.Boleto,
      title: t("components.payment.methods.boleto.title"),
      description: t("components.payment.methods.boleto.description"),
      icon: <BoletoIcon />,
      instantPayment: false
    }
  ];

  // Handler para seleção de método
  const handleMethodSelect = useCallback(
    (method: PaymentType) => {
      console.log("💳 [PAYMENT-METHOD-SELECTOR] Método selecionado:", method);
      onMethodSelect(method);
      // Só limpa seleção de cartão salvo quando seleciona método diferente de cartão de crédito
      if (onSelectSavedCard && method !== PaymentType.CreditCard) {
        console.log(
          "🧹 [PAYMENT-METHOD-SELECTOR] Limpando cartão salvo (método não é cartão)"
        );
        onSelectSavedCard(undefined as any);
      }
    },
    [onMethodSelect, onSelectSavedCard]
  );

  // Handler para seleção de cartão salvo
  const handleSavedCardSelect = useCallback(
    (card: CreditCard) => {
      console.log(
        "💾 [PAYMENT-METHOD-SELECTOR] 🔧 DEBUG - Cartão salvo selecionado:",
        {
          cardId: card.id,
          cardBrand: card.brand,
          hasOnSelectSavedCard: !!onSelectSavedCard,
          hasOnMethodSelect: !!onMethodSelect
        }
      );

      if (onSelectSavedCard) {
        console.log(
          "🔄 [PAYMENT-METHOD-SELECTOR] 🔧 DEBUG - Chamando onSelectSavedCard"
        );
        onSelectSavedCard(card);

        // Seleciona automaticamente cartão de crédito PRESERVANDO o cartão selecionado
        console.log(
          "🔄 [PAYMENT-METHOD-SELECTOR] 🔧 DEBUG - Auto-selecionando cartão de crédito (preservando cartão)"
        );
        onMethodSelect(PaymentType.CreditCard, true);
      } else {
        console.log(
          "❌ [PAYMENT-METHOD-SELECTOR] 🔧 DEBUG - onSelectSavedCard não está definido!"
        );
      }
    },
    [onSelectSavedCard, onMethodSelect]
  );

  // Renderiza um método de pagamento
  const renderPaymentMethod = useCallback(
    (method: (typeof paymentMethods)[0]) => {
      const isSelected = selectedMethod === method.type;

      return (
        <TouchableOpacity
          key={method.type}
          style={[styles.methodCard, isSelected && styles.selectedMethodCard]}
          onPress={() => handleMethodSelect(method.type)}
        >
          <View style={styles.methodHeader}>
            <View style={styles.methodIcon}>{method.icon}</View>
            <View style={styles.methodInfo}>
              <Text
                style={[
                  styles.methodTitle,
                  isSelected && styles.selectedMethodTitle
                ]}
              >
                {method.title}
              </Text>
              <Text
                style={[
                  styles.methodDescription,
                  isSelected && styles.selectedMethodDescription
                ]}
              >
                {method.description}
              </Text>
            </View>
            {isSelected && (
              <View style={styles.checkIcon}>
                <CheckIcon />
              </View>
            )}
          </View>
          {method.instantPayment && (
            <View style={styles.instantBadge}>
              <Text style={styles.instantBadgeText}>
                {t("components.payment.methods.instantPayment")}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      );
    },
    [selectedMethod, handleMethodSelect, t]
  );

  // Renderiza um cartão salvo
  const renderSavedCard = useCallback(
    (card: CreditCard) => {
      const isSelected = selectedSavedCard?.id === card.id;

      // Extrai os últimos 4 dígitos do campo number ou usa lastFourDigits
      const lastFourDigits =
        card.lastFourDigits || (card.number ? card.number.slice(-4) : "****");

      // Mapeia o brand numérico para nome
      const getBrandName = (brand?: string | number) => {
        if (typeof brand === "number") {
          switch (brand) {
            case 0:
              return "Visa";
            case 1:
              return "Mastercard";
            case 2:
              return "Elo";
            case 3:
              return "American Express";
            default:
              return "Cartão";
          }
        }
        return brand || "Cartão";
      };

      console.log(
        "💳 [PAYMENT-METHOD-SELECTOR] 🔧 DEBUG SELEÇÃO - Renderizando cartão:",
        {
          cardId: card.id,
          selectedSavedCardId: selectedSavedCard?.id,
          isSelected,
          brand: card.brand,
          brandName: getBrandName(card.brand),
          lastFourDigits,
          holderName: card.holderName,
          hasOnSelectSavedCard: !!onSelectSavedCard
        }
      );

      return (
        <TouchableOpacity
          key={card.id}
          style={[
            styles.savedMethodCard,
            isSelected && styles.selectedSavedMethodCard
          ]}
          onPress={() => {
            console.log(
              "👆 [PAYMENT-METHOD-SELECTOR] 🔧 DEBUG TOQUE - Cartão tocado:",
              card.id
            );
            console.log(
              "👆 [PAYMENT-METHOD-SELECTOR] 🔧 DEBUG TOQUE - Dados completos:",
              {
                cardId: card.id,
                cardBrand: card.brand,
                cardHolder: card.holderName,
                currentSelected: selectedSavedCard?.id,
                willBeSelected: card.id,
                isCurrentlySelected: selectedSavedCard?.id === card.id
              }
            );
            handleSavedCardSelect(card);
          }}
          onPressIn={() => {
            console.log(
              "👇 [PAYMENT-METHOD-SELECTOR] 🔧 DEBUG - onPressIn chamado para cartão:",
              card.id
            );
          }}
          onPressOut={() => {
            console.log(
              "👆 [PAYMENT-METHOD-SELECTOR] 🔧 DEBUG - onPressOut chamado para cartão:",
              card.id
            );
          }}
          activeOpacity={0.7}
          disabled={false}
        >
          <View style={styles.savedMethodHeader} pointerEvents="none">
            <View style={styles.methodIcon}>
              <Text>💳</Text>
            </View>
            <View style={styles.savedMethodInfo}>
              <Text
                style={[
                  styles.savedMethodTitle,
                  isSelected && styles.selectedSavedMethodTitle
                ]}
              >
                {getBrandName(card.brand)} •••• {lastFourDigits}
              </Text>
              <Text
                style={[
                  styles.savedMethodDescription,
                  isSelected && styles.selectedSavedMethodDescription
                ]}
              >
                {card.holderName || "Titular do cartão"}
              </Text>
            </View>
            {card.isDefault && (
              <View style={styles.defaultBadge}>
                <Text style={styles.defaultBadgeText}>
                  {t("components.payment.methods.default")}
                </Text>
              </View>
            )}
            {isSelected && (
              <View style={styles.checkIcon}>
                <Text style={{color: "green", fontSize: 20}}>✓</Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
      );
    },
    [selectedSavedCard, handleSavedCardSelect, t]
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Text style={styles.sectionTitle}>
        {t("components.payment.methodSelector.title")}
      </Text>

      {/* Cartões salvos */}
      {showSavedCards && savedCreditCards.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.subsectionTitle}>
            {t("components.payment.methodSelector.savedCards")}
          </Text>
          {savedCreditCards.map(renderSavedCard)}

          {/* Botão para adicionar novo cartão - AÇÃO, não seleção */}
          {onAddNewCard && (
            <TouchableOpacity
              style={styles.addNewCardButton}
              onPress={onAddNewCard}
            >
              <View style={styles.addNewCardContent}>
                <View style={styles.addNewCardIcon}>
                  <Text style={styles.addNewCardIconText}>+</Text>
                </View>
                <Text style={styles.addNewCardText}>
                  {t("components.payment.methodSelector.addNewCard")}
                </Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Novos métodos de pagamento */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>
          {t("components.payment.methodSelector.newMethods")}
        </Text>
        {paymentMethods.map(renderPaymentMethod)}
      </View>
    </ScrollView>
  );
};

export default PaymentMethodSelector;
