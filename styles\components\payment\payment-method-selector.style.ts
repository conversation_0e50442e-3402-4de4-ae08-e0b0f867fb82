/**
 * Estilos para o componente PaymentMethodSelector
 * Seguindo os padrões de design do projeto
 */

import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },

  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 20,
    fontFamily: stylesConstants.fonts.inter
  },

  section: {
    marginBottom: 24
  },

  subsectionTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 12,
    fontFamily: stylesConstants.fonts.inter
  },

  methodCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: stylesConstants.colors.borderDefault
  },

  selectedMethodCard: {
    borderColor: stylesConstants.colors.brand.brand500,
    backgroundColor: stylesConstants.colors.highlightBackground
  },

  methodHeader: {
    flexDirection: "row",
    alignItems: "center"
  },

  methodIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: stylesConstants.colors.mainBackground,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },

  methodInfo: {
    flex: 1
  },

  methodTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 4,
    fontFamily: stylesConstants.fonts.inter
  },

  selectedMethodTitle: {
    color: stylesConstants.colors.brand.brand500
  },

  methodDescription: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.inter
  },

  selectedMethodDescription: {
    color: stylesConstants.colors.textPrimary
  },

  checkIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: stylesConstants.colors.brand.brand500,
    justifyContent: "center",
    alignItems: "center"
  },

  instantBadge: {
    backgroundColor: stylesConstants.colors.green400,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    alignSelf: "flex-start",
    marginTop: 8
  },

  instantBadgeText: {
    fontSize: 12,
    fontWeight: "500",
    color: stylesConstants.colors.white,
    fontFamily: stylesConstants.fonts.inter
  },

  savedMethodCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },

  selectedSavedMethodCard: {
    borderColor: stylesConstants.colors.brand.brand500,
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderWidth: 2
  },

  savedMethodHeader: {
    flexDirection: "row",
    alignItems: "center"
  },

  savedMethodInfo: {
    flex: 1
  },

  savedMethodTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 4,
    fontFamily: stylesConstants.fonts.inter
  },

  selectedSavedMethodTitle: {
    color: stylesConstants.colors.brand.brand500
  },

  savedMethodDescription: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.inter
  },

  selectedSavedMethodDescription: {
    color: stylesConstants.colors.textPrimary
  },

  defaultBadge: {
    backgroundColor: stylesConstants.colors.alert400,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginRight: 8
  },

  defaultBadgeText: {
    fontSize: 12,
    fontWeight: "500",
    color: stylesConstants.colors.white,
    fontFamily: stylesConstants.fonts.inter
  },

  // Estilos para botão "Adicionar novo cartão"
  addNewCardButton: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    borderWidth: 2,
    borderColor: stylesConstants.colors.borderDefault,
    borderStyle: "dashed"
  },

  selectedAddNewCardButton: {
    borderColor: stylesConstants.colors.brand.brand500,
    backgroundColor: stylesConstants.colors.highlightBackground,
    borderStyle: "solid"
  },

  addNewCardContent: {
    flexDirection: "row",
    alignItems: "center"
  },

  addNewCardIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: stylesConstants.colors.brand.brand25,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },

  addNewCardIconText: {
    fontSize: 20,
    fontWeight: "600",
    color: stylesConstants.colors.brand.brand500,
    fontFamily: stylesConstants.fonts.inter
  },

  addNewCardText: {
    flex: 1,
    fontSize: 16,
    fontWeight: "500",
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.inter
  },

  selectedAddNewCardText: {
    color: stylesConstants.colors.brand.brand500
  }
});

export default styles;
