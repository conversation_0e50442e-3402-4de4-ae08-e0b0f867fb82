{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-66:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03b3dfb7e6b29424b14ebc5db8bcef20\\transformed\\play-services-basement-18.3.0\\res\\values-hy\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5891", "endColumns": "150", "endOffsets": "6037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,102", "endOffsets": "154,257,368,471"}, "to": {"startLines": "70,77,78,79", "startColumns": "4,4,4,4", "startOffsets": "7277,7986,8089,8200", "endColumns": "103,102,110,102", "endOffsets": "7376,8084,8195,8298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "40,41,42,43,44,45,46,165", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3745,3845,3950,4048,4147,4252,4354,15749", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3840,3945,4043,4142,4247,4349,4460,15845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\22d1bdfca510dffa95f9466f4e112b1d\\transformed\\play-services-base-18.0.1\\res\\values-hy\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,586,690,840,972,1095,1204,1367,1471,1635,1767,1925,2087,2148,2211", "endColumns": "101,160,129,103,149,131,122,108,162,103,163,131,157,161,60,62,77", "endOffsets": "294,455,585,689,839,971,1094,1203,1366,1470,1634,1766,1924,2086,2147,2210,2288"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4848,4954,5119,5253,5361,5515,5651,5778,6042,6209,6317,6485,6621,6783,6949,7014,7081", "endColumns": "105,164,133,107,153,135,126,112,166,107,167,135,161,165,64,66,81", "endOffsets": "4949,5114,5248,5356,5510,5646,5773,5886,6204,6312,6480,6616,6778,6944,7009,7076,7158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,423,523,633,722,828,945,1027,1107,1198,1291,1386,1480,1580,1673,1768,1862,1953,2044,2127,2233,2339,2438,2548,2656,2757,2927,15280", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "418,518,628,717,823,940,1022,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2122,2228,2334,2433,2543,2651,2752,2922,3019,15358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,281,349,421,496", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "127,195,276,344,416,491,565"}, "to": {"startLines": "50,94,95,97,111,163,164", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4771,10067,10135,10278,11311,15600,15675", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "4843,10130,10211,10341,11378,15670,15744"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,364,478,566,731,861,975,1090,1216,1339,1470,1589,1751,1854,2019,2150,2282,2417,2540,2661,2763,2910,3015,3154,3255,3390", "endColumns": "171,136,113,87,164,129,113,114,125,122,130,118,161,102,164,130,131,134,122,120,101,146,104,138,100,134,94", "endOffsets": "222,359,473,561,726,856,970,1085,1211,1334,1465,1584,1746,1849,2014,2145,2277,2412,2535,2656,2758,2905,3010,3149,3250,3385,3480"}, "to": {"startLines": "33,34,69,71,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,158,166,167,168,169,170,171,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3024,3196,7163,7381,7691,7856,8303,8417,8532,8658,8781,8912,9031,9193,9296,9461,9592,9724,9859,15159,15850,15952,16099,16204,16343,16444,16579", "endColumns": "171,136,113,87,164,129,113,114,125,122,130,118,161,102,164,130,131,134,122,120,101,146,104,138,100,134,94", "endOffsets": "3191,3328,7272,7464,7851,7981,8412,8527,8653,8776,8907,9026,9188,9291,9456,9587,9719,9854,9977,15275,15947,16094,16199,16338,16439,16574,16669"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1044,1108,1205,1290,1352,1439,1501,1565,1626,1693,1754,1808,1930,1987,2047,2101,2182,2317,2401,2477,2567,2646,2731,2867,2942,3017,3160,3255,3335,3391,3444,3510,3584,3663,3734,3817,3888,3964,4040,4117,4223,4311,4391,4487,4583,4657,4735,4835,4886,4970,5039,5126,5217,5279,5343,5406,5477,5582,5688,5788,5891,5951,6008,6093,6176,6250", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84,82,73,79", "endOffsets": "260,336,412,492,584,672,767,897,978,1039,1103,1200,1285,1347,1434,1496,1560,1621,1688,1749,1803,1925,1982,2042,2096,2177,2312,2396,2472,2562,2641,2726,2862,2937,3012,3155,3250,3330,3386,3439,3505,3579,3658,3729,3812,3883,3959,4035,4112,4218,4306,4386,4482,4578,4652,4730,4830,4881,4965,5034,5121,5212,5274,5338,5401,5472,5577,5683,5783,5886,5946,6003,6088,6171,6245,6325"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,72,73,74,93,96,98,99,100,101,102,103,104,105,106,107,108,109,110,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3333,3409,3485,3565,3657,4465,4560,4690,7469,7530,7594,9982,10216,10346,10433,10495,10559,10620,10687,10748,10802,10924,10981,11041,11095,11176,11383,11467,11543,11633,11712,11797,11933,12008,12083,12226,12321,12401,12457,12510,12576,12650,12729,12800,12883,12954,13030,13106,13183,13289,13377,13457,13553,13649,13723,13801,13901,13952,14036,14105,14192,14283,14345,14409,14472,14543,14648,14754,14854,14957,15017,15074,15363,15446,15520", "endLines": "5,35,36,37,38,39,47,48,49,72,73,74,93,96,98,99,100,101,102,103,104,105,106,107,108,109,110,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,160,161,162", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84,82,73,79", "endOffsets": "310,3404,3480,3560,3652,3740,4555,4685,4766,7525,7589,7686,10062,10273,10428,10490,10554,10615,10682,10743,10797,10919,10976,11036,11090,11171,11306,11462,11538,11628,11707,11792,11928,12003,12078,12221,12316,12396,12452,12505,12571,12645,12724,12795,12878,12949,13025,13101,13178,13284,13372,13452,13548,13644,13718,13796,13896,13947,14031,14100,14187,14278,14340,14404,14467,14538,14643,14749,14849,14952,15012,15069,15154,15441,15515,15595"}}]}]}