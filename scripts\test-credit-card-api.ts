/**
 * Credit Card API Testing Script
 * Simple validation script to test the credit card functionality
 */

import { CreditCardsService } from '../services/api/credit-cards/credit-cards.service';
import { transformCreditCardToMemberCard } from '../utils/credit-card-transform';
import { CreateCreditCardFormData } from '../models/api/credit-cards.models';

// Test data
const testCardData: CreateCreditCardFormData = {
  holderName: "Test User",
  number: "****************", // Valid test Visa number
  expiryMonth: 12,
  expiryYear: 2025,
  cvv: "123"
};

const invalidCardData: CreateCreditCardFormData = {
  holderName: "",
  number: "123",
  expiryMonth: 13,
  expiryYear: 2020,
  cvv: "12"
};

/**
 * Test validation functions
 */
function testValidation() {
  console.log('🧪 Testing validation functions...');
  
  // Test valid data
  const validErrors = CreditCardsService.validateCreditCardData(testCardData);
  console.log('✅ Valid data errors:', validErrors.length === 0 ? 'None (correct)' : validErrors);
  
  // Test invalid data
  const invalidErrors = CreditCardsService.validateCreditCardData(invalidCardData);
  console.log('❌ Invalid data errors:', invalidErrors.length > 0 ? `${invalidErrors.length} errors (correct)` : 'None (incorrect)');
  
  // Test card number formatting
  const formatted = CreditCardsService.formatCardNumber('****************');
  console.log('💳 Formatted card number:', formatted === '4111 1111 1111 1111' ? '✅ Correct' : '❌ Incorrect');
  
  // Test card brand detection
  const visaBrand = CreditCardsService.detectCardBrand('****************');
  const mastercardBrand = CreditCardsService.detectCardBrand('****************');
  const amexBrand = CreditCardsService.detectCardBrand('***************');
  
  console.log('🏷️ Brand detection:');
  console.log('  Visa:', visaBrand === 'visa' ? '✅' : '❌');
  console.log('  Mastercard:', mastercardBrand === 'mastercard' ? '✅' : '❌');
  console.log('  Amex:', amexBrand === 'american-express' ? '✅' : '❌');
  
  // Test Luhn validation
  const validLuhn = CreditCardsService.validateCardNumber('****************');
  const invalidLuhn = CreditCardsService.validateCardNumber('****************');
  
  console.log('🔢 Luhn validation:');
  console.log('  Valid number:', validLuhn ? '✅' : '❌');
  console.log('  Invalid number:', !invalidLuhn ? '✅' : '❌');
}

/**
 * Test data transformation
 */
function testTransformation() {
  console.log('\n🔄 Testing data transformation...');
  
  const mockCreditCard = {
    id: 1,
    holderName: "John Doe",
    lastFourDigits: "1234",
    expiryMonth: 12,
    expiryYear: 2025,
    brand: "visa",
    userId: 1,
    createdAt: "2023-01-01T00:00:00Z",
    createdBy: "user"
  };
  
  try {
    const memberCard = transformCreditCardToMemberCard(mockCreditCard);
    
    console.log('✅ Transformation successful:');
    console.log('  ID:', memberCard.id === '1' ? '✅' : '❌');
    console.log('  Name:', memberCard.name === 'John Doe' ? '✅' : '❌');
    console.log('  Card Type:', memberCard.cardType === 'visa' ? '✅' : '❌');
    console.log('  Expiry Date:', memberCard.expiryDate === '12/2025' ? '✅' : '❌');
    console.log('  Card Number:', memberCard.cardNumber === '1234' ? '✅' : '❌');
  } catch (error) {
    console.log('❌ Transformation failed:', error);
  }
}

/**
 * Test API endpoints (mock)
 */
async function testAPIEndpoints() {
  console.log('\n🌐 Testing API endpoint structure...');
  
  // Test if service methods exist
  const methods = [
    'getCreditCards',
    'getCreditCard', 
    'createCreditCard',
    'validateCreditCardData',
    'formatCardNumber',
    'detectCardBrand',
    'validateCardNumber'
  ];
  
  methods.forEach(method => {
    const exists = typeof CreditCardsService[method as keyof typeof CreditCardsService] === 'function';
    console.log(`  ${method}:`, exists ? '✅' : '❌');
  });
}

/**
 * Test error handling
 */
function testErrorHandling() {
  console.log('\n🚨 Testing error handling...');
  
  try {
    // Test with null/undefined data
    const nullErrors = CreditCardsService.validateCreditCardData(null as any);
    console.log('  Null data handling:', Array.isArray(nullErrors) ? '✅' : '❌');
  } catch (error) {
    console.log('  Null data handling: ✅ (throws error as expected)');
  }
  
  try {
    // Test with empty strings
    const emptyData = {
      holderName: "",
      number: "",
      expiryMonth: 0,
      expiryYear: 0,
      cvv: ""
    };
    const emptyErrors = CreditCardsService.validateCreditCardData(emptyData);
    console.log('  Empty data validation:', emptyErrors.length > 0 ? '✅' : '❌');
  } catch (error) {
    console.log('  Empty data validation: ❌ (should not throw)');
  }
}

/**
 * Test edge cases
 */
function testEdgeCases() {
  console.log('\n🎯 Testing edge cases...');
  
  // Test very long card numbers
  const longNumber = '****************1111111111111111';
  const longNumberValid = CreditCardsService.validateCardNumber(longNumber);
  console.log('  Long card number:', !longNumberValid ? '✅' : '❌');
  
  // Test very short card numbers
  const shortNumber = '411';
  const shortNumberValid = CreditCardsService.validateCardNumber(shortNumber);
  console.log('  Short card number:', !shortNumberValid ? '✅' : '❌');
  
  // Test special characters in card number
  const specialChars = '4111-1111-1111-1111';
  const formatted = CreditCardsService.formatCardNumber(specialChars);
  console.log('  Special chars formatting:', formatted === '4111 1111 1111 1111' ? '✅' : '❌');
  
  // Test future expiry dates
  const futureYear = new Date().getFullYear() + 10;
  const futureData = {
    ...testCardData,
    expiryYear: futureYear
  };
  const futureErrors = CreditCardsService.validateCreditCardData(futureData);
  console.log('  Future expiry date:', futureErrors.length === 0 ? '✅' : '❌');
}

/**
 * Main test runner
 */
export function runCreditCardTests() {
  console.log('🚀 Starting Credit Card API Tests\n');
  
  testValidation();
  testTransformation();
  testAPIEndpoints();
  testErrorHandling();
  testEdgeCases();
  
  console.log('\n✨ Credit Card API Tests Complete!');
  console.log('\n📋 Summary:');
  console.log('- Validation functions: Tested');
  console.log('- Data transformation: Tested');
  console.log('- API structure: Verified');
  console.log('- Error handling: Tested');
  console.log('- Edge cases: Tested');
  console.log('\n🎉 Credit card functionality is ready for use!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runCreditCardTests();
}
