/**
 * Referral/Indication models based on ClubM API
 * Matches the API schemas for /api/app/indications endpoints
 */

import {PaginationRequest, PaginationResponse} from "./common.models";

// Referral Status enum (inferred from common API patterns)
export enum ReferralStatus {
  PENDING = "pending",
  ACCEPTED = "accepted",
  REJECTED = "rejected",
  EXPIRED = "expired",
  CANCELLED = "cancelled"
}

// API Request interfaces (based on CreateIndicationViewModel)
export interface CreateReferralRequest {
  name: string;
  email: string;
  phone?: string;
  document?: string;
  message?: string;
  notes?: string;
}

// Simple validation functions for creating referrals
export const validateReferralRequest = (
  data: CreateReferralRequest
): string[] => {
  const errors: string[] = [];

  if (!data.name || data.name.trim().length === 0) {
    errors.push("Nome é obrigatório");
  }
  if (data.name && data.name.length > 100) {
    errors.push("Nome muito longo");
  }

  if (!data.email || data.email.trim().length === 0) {
    errors.push("Email é obrigatório");
  }
  if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push("Email inválido");
  }
  if (data.email && data.email.length > 255) {
    errors.push("Email muito longo");
  }

  if (data.message && data.message.length > 500) {
    errors.push("Mensagem muito longa");
  }

  if (data.notes && data.notes.length > 1000) {
    errors.push("Notas muito longas");
  }

  return errors;
};

// API Response interfaces (based on Indication response model)
export interface Referral {
  id: number;
  name: string;
  email: string;
  phone?: string;
  document?: string;
  message?: string;
  notes?: string;
  status: ReferralStatus;
  createdAt: string;
  updatedAt?: string;
  acceptedAt?: string;
  rejectedAt?: string;
  expiredAt?: string;
  referrerId: number;
  referrer?: ReferralUser;
  invitedUser?: ReferralUser;
}

// User information for referrals
export interface ReferralUser {
  id: number;
  name: string;
  email: string;
  phone?: string;
  document?: string;
  avatar?: string;
  isActive: boolean;
  createdAt: string;
}

// Query parameters for listing referrals
export interface ReferralsListParams extends PaginationRequest {
  search?: string;
  status?: ReferralStatus;
  createdAtStart?: string;
  createdAtEnd?: string;
}

// Validation function for referrals list params
export const validateReferralsListParams = (
  params: ReferralsListParams
): string[] => {
  const errors: string[] = [];

  if (params.page && params.page < 1) {
    errors.push("Página deve ser maior que 0");
  }

  if (params.pageSize && (params.pageSize < 1 || params.pageSize > 100)) {
    errors.push("Tamanho da página deve estar entre 1 e 100");
  }

  return errors;
};

// Paginated response for referrals
export interface ReferralsResponse extends PaginationResponse<Referral> {}

// Statistics for referrals dashboard
export interface ReferralStats {
  totalReferrals: number;
  pendingReferrals: number;
  acceptedReferrals: number;
  rejectedReferrals: number;
  conversionRate: number;
  thisMonthReferrals: number;
  lastMonthReferrals: number;
}

// Contact search for invitations
export interface ContactSearchParams {
  search?: string;
  page?: number;
  pageSize?: number;
}

export interface ContactSearchResult {
  id: number;
  name: string;
  email: string;
  phone?: string;
  document?: string;
  avatar?: string;
  isAlreadyInvited: boolean;
  isAlreadyMember: boolean;
}

export interface ContactSearchResponse
  extends PaginationResponse<ContactSearchResult> {}

// Bulk referral creation
export interface BulkReferralRequest {
  contacts: CreateReferralRequest[];
  message?: string;
}

export interface BulkReferralResponse {
  successful: number;
  failed: number;
  errors: Array<{
    contact: CreateReferralRequest;
    error: string;
  }>;
  referrals: Referral[];
}

// Referral invitation resend
export interface ResendInvitationRequest {
  referralId: number;
  message?: string;
}

// Export types for easier imports
export type CreateReferralFormData = CreateReferralRequest;
export type ReferralsListFormData = ReferralsListParams;
