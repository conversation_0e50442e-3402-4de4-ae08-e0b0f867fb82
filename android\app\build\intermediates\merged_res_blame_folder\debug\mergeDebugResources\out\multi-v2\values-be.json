{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-66:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03b3dfb7e6b29424b14ebc5db8bcef20\\transformed\\play-services-basement-18.3.0\\res\\values-be\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6023", "endColumns": "145", "endOffsets": "6164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,272,384", "endColumns": "108,107,111,106", "endOffsets": "159,267,379,486"}, "to": {"startLines": "72,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7363,8102,8210,8322", "endColumns": "108,107,111,106", "endOffsets": "7467,8205,8317,8424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "432,552,655,771,857,962,1081,1161,1238,1330,1424,1519,1613,1708,1802,1898,1993,2085,2177,2258,2364,2469,2567,2675,2781,2889,3062,15570", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "547,650,766,852,957,1076,1156,1233,1325,1419,1514,1608,1703,1797,1893,1988,2080,2172,2253,2359,2464,2562,2670,2776,2884,3057,3157,15647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae5dcacd584dd15cca165a8a53f860c\\transformed\\material-1.12.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1197,1264,1357,1434,1497,1613,1676,1745,1804,1875,1934,1988,2109,2170,2233,2287,2360,2482,2570,2646,2737,2818,2901,3053,3139,3226,3359,3450,3533,3590,3641,3707,3779,3856,3927,4010,4085,4162,4244,4320,4428,4517,4599,4690,4786,4860,4941,5036,5090,5172,5238,5325,5411,5473,5537,5600,5669,5779,5892,5995,6102,6163,6218,6298,6383,6459", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,76,76,81,96,91,96,131,82,77,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,132,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79,84,75,78", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1192,1259,1352,1429,1492,1608,1671,1740,1799,1870,1929,1983,2104,2165,2228,2282,2355,2477,2565,2641,2732,2813,2896,3048,3134,3221,3354,3445,3528,3585,3636,3702,3774,3851,3922,4005,4080,4157,4239,4315,4423,4512,4594,4685,4781,4855,4936,5031,5085,5167,5233,5320,5406,5468,5532,5595,5664,5774,5887,5990,6097,6158,6213,6293,6378,6454,6533"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,74,75,76,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3468,3545,3622,3704,3801,4624,4721,4853,7566,7644,7711,10194,10427,10561,10677,10740,10809,10868,10939,10998,11052,11173,11234,11297,11351,11424,11613,11701,11777,11868,11949,12032,12184,12270,12357,12490,12581,12664,12721,12772,12838,12910,12987,13058,13141,13216,13293,13375,13451,13559,13648,13730,13821,13917,13991,14072,14167,14221,14303,14369,14456,14542,14604,14668,14731,14800,14910,15023,15126,15233,15294,15349,15652,15737,15813", "endLines": "7,37,38,39,40,41,49,50,51,74,75,76,95,98,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,162,163,164", "endColumns": "12,76,76,81,96,91,96,131,82,77,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,132,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79,84,75,78", "endOffsets": "427,3540,3617,3699,3796,3888,4716,4848,4931,7639,7706,7799,10266,10485,10672,10735,10804,10863,10934,10993,11047,11168,11229,11292,11346,11419,11541,11696,11772,11863,11944,12027,12179,12265,12352,12485,12576,12659,12716,12767,12833,12905,12982,13053,13136,13211,13288,13370,13446,13554,13643,13725,13816,13912,13986,14067,14162,14216,14298,14364,14451,14537,14599,14663,14726,14795,14905,15018,15121,15228,15289,15344,15424,15732,15808,15887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a89efe01639eb7dad7f1852c2dc2010d\\transformed\\react-android-0.79.5-debug\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,138,209,294,365", "endColumns": "82,70,84,70,66", "endOffsets": "133,204,289,360,427"}, "to": {"startLines": "52,96,97,99,113", "startColumns": "4,4,4,4,4", "startOffsets": "4936,10271,10342,10490,11546", "endColumns": "82,70,84,70,66", "endOffsets": "5014,10337,10422,10556,11608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,361,473,567,739,865,981,1111,1262,1401,1530,1657,1808,1907,2081,2209,2351,2503,2630,2771,2874,3023,3131,3285,3395,3551", "endColumns": "175,129,111,93,171,125,115,129,150,138,128,126,150,98,173,127,141,151,126,140,102,148,107,153,109,155,122", "endOffsets": "226,356,468,562,734,860,976,1106,1257,1396,1525,1652,1803,1902,2076,2204,2346,2498,2625,2766,2869,3018,3126,3280,3390,3546,3669"}, "to": {"startLines": "35,36,71,73,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,160,166,167,168,169,170,171,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3162,3338,7251,7472,7804,7976,8429,8545,8675,8826,8965,9094,9221,9372,9471,9645,9773,9915,10067,15429,15993,16096,16245,16353,16507,16617,16773", "endColumns": "175,129,111,93,171,125,115,129,150,138,128,126,150,98,173,127,141,151,126,140,102,148,107,153,109,155,122", "endOffsets": "3333,3463,7358,7561,7971,8097,8540,8670,8821,8960,9089,9216,9367,9466,9640,9768,9910,10062,10189,15565,16091,16240,16348,16502,16612,16768,16891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "42,43,44,45,46,47,48,165", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3893,3991,4093,4193,4294,4400,4503,15892", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3986,4088,4188,4289,4395,4498,4619,15988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\22d1bdfca510dffa95f9466f4e112b1d\\transformed\\play-services-base-18.0.1\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5019,5126,5288,5413,5523,5678,5804,5919,6169,6331,6438,6601,6729,6882,7041,7110,7172", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "5121,5283,5408,5518,5673,5799,5914,6018,6326,6433,6596,6724,6877,7036,7105,7167,7246"}}]}]}