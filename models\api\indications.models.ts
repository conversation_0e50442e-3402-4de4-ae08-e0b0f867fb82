import {z} from "zod";

/**
 * Status de uma indicação
 */
export enum IndicationStatus {
  PENDING = "pending",
  ACCEPTED = "accepted",
  REJECTED = "rejected",
  CANCELLED = "cancelled",
  EXPIRED = "expired"
}

/**
 * Schema para criar uma nova indicação
 */
export const CreateIndicationSchema = z.object({
  name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  email: z.string().email("Email inválido"),
  phone: z
    .string()
    .min(10, "Telefone deve ter pelo menos 10 dígitos")
    .optional(),
  message: z.string().optional()
});

export type CreateIndicationRequest = z.infer<typeof CreateIndicationSchema>;

/**
 * Modelo de uma indicação
 */
export interface Indication {
  id: string;
  referrerUserId: string;
  referredName: string;
  referredEmail: string;
  referredPhone?: string;
  status: IndicationStatus;
  message?: string;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
  acceptedAt?: string;
}

/**
 * Parâmetros para listar indicações
 */
export interface IndicationsListParams {
  search?: string;
  status?: IndicationStatus;
  createdAtStart?: string;
  createdAtEnd?: string;
  page?: number;
  pageSize?: number;
}

/**
 * Response da API para criação de indicação
 */
export interface CreateIndicationResponse {
  success: boolean;
  message: string;
  data?: Indication;
}

/**
 * Response da API para listagem de indicações
 */
export interface IndicationsListResponse {
  data: Indication[];
  pagination: {
    page: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

/**
 * Estatísticas de indicações
 */
export interface IndicationStats {
  totalIndications: number;
  pendingIndications: number;
  acceptedIndications: number;
  rejectedIndications: number;
  cancelledIndications: number;
  expiredIndications: number;
  conversionRate: number;
  thisMonthIndications: number;
  lastMonthIndications: number;
}
