import React, {useEffect, useRef} from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  Animated,
  Dimensions,
  SafeAreaView
} from "react-native";
import BellIcon from "../icons/bell-icon";
import HelpCircleIcon from "../icons/help-circle-icon";
import ChevronLeftIcon from "../icons/chevron-left-icon";
import ChevronRightIcon from "../icons/chevron-right-icon";
import styles from "../../styles/components/messages-screen/message-filter-drawer.style";

export interface MessageFilters {
  unread: boolean;
  unanswered: boolean;
  older: boolean;
  recent: boolean;
}

export interface MessageFilterDrawerProps {
  visible: boolean;
  filters: MessageFilters;
  onFiltersChange: (filters: MessageFilters) => void;
  onClose: () => void;
  onClear: () => void;
}

const MessageFilterDrawer: React.FC<MessageFilterDrawerProps> = ({
  visible,
  filters,
  onFiltersChange,
  onClose,
  onClear
}) => {
  const slideAnim = useRef(new Animated.Value(0)).current;
  const screenHeight = Dimensions.get("window").height;

  const filterOptions = [
    {
      key: "unread" as keyof MessageFilters,
      label: "Não lidas",
      icon: <BellIcon width={12} height={12} replaceColor={filters.unread ? "#1D2939" : "#DFE9F0"} />
    },
    {
      key: "unanswered" as keyof MessageFilters,
      label: "Não respondidas",
      icon: <HelpCircleIcon width={12} height={12} replaceColor={filters.unanswered ? "#1D2939" : "#DFE9F0"} />
    },
    {
      key: "older" as keyof MessageFilters,
      label: "Mais antigas",
      icon: <ChevronLeftIcon width={12} height={12} stroke={filters.older ? "#1D2939" : "#DFE9F0"} />
    },
    {
      key: "recent" as keyof MessageFilters,
      label: "Mais recentes",
      icon: <ChevronRightIcon width={12} height={12} stroke={filters.recent ? "#1D2939" : "#DFE9F0"} />
    }
  ];

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true
      }).start();
    }
  }, [visible, slideAnim]);

  const handleFilterToggle = (filterKey: keyof MessageFilters) => {
    const newFilters = {...filters};
    
    // Se for "older" ou "recent", são mutuamente exclusivos
    if (filterKey === "older") {
      newFilters.older = !filters.older;
      if (newFilters.older) {
        newFilters.recent = false;
      }
    } else if (filterKey === "recent") {
      newFilters.recent = !filters.recent;
      if (newFilters.recent) {
        newFilters.older = false;
      }
    } else {
      // Para "unread" e "unanswered", apenas toggle
      newFilters[filterKey] = !filters[filterKey];
    }

    onFiltersChange(newFilters);
  };

  const handleBackdropPress = () => {
    onClose();
  };

  const translateY = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [screenHeight, 0]
  });

  const backdropOpacity = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.5]
  });

  const isFilterSelected = (filterKey: keyof MessageFilters) => {
    return filters[filterKey];
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <Animated.View style={[styles.backdrop, {opacity: backdropOpacity}]}>
          <TouchableOpacity
            style={styles.backdropTouchable}
            activeOpacity={1}
            onPress={handleBackdropPress}
          />
        </Animated.View>

        <Animated.View
          style={[
            styles.drawer,
            {
              transform: [{translateY}]
            }
          ]}
        >
          <SafeAreaView style={styles.safeArea}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.handle} />
              <Text style={styles.title}>Filtros de mensagens</Text>
            </View>

            {/* Content */}
            <View style={styles.content}>
              {/* Tipos de filtro */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Tipos de filtro</Text>
                <View style={styles.filtersContainer}>
                  <View style={styles.filtersRow}>
                    {filterOptions.slice(0, 2).map((option) => (
                      <TouchableOpacity
                        key={option.key}
                        style={[
                          styles.filterBadge,
                          isFilterSelected(option.key) && styles.filterBadgeSelected
                        ]}
                        onPress={() => handleFilterToggle(option.key)}
                      >
                        {option.icon}
                        <Text
                          style={[
                            styles.filterBadgeText,
                            isFilterSelected(option.key) && styles.filterBadgeTextSelected
                          ]}
                        >
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                  <View style={styles.filtersRow}>
                    {filterOptions.slice(2).map((option) => (
                      <TouchableOpacity
                        key={option.key}
                        style={[
                          styles.filterBadge,
                          isFilterSelected(option.key) && styles.filterBadgeSelected
                        ]}
                        onPress={() => handleFilterToggle(option.key)}
                      >
                        {option.icon}
                        <Text
                          style={[
                            styles.filterBadgeText,
                            isFilterSelected(option.key) && styles.filterBadgeTextSelected
                          ]}
                        >
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              </View>
            </View>

            {/* Clear Button */}
            <View style={styles.buttonsContainer}>
              <TouchableOpacity style={styles.clearButton} onPress={onClear}>
                <Text style={styles.clearButtonText}>Limpar filtros</Text>
              </TouchableOpacity>
            </View>
          </SafeAreaView>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default MessageFilterDrawer;
