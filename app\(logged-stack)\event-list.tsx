import React, {useCallback, useState, useMemo} from "react";
import <PERSON><PERSON>ithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import Search from "../../components/search";
import styles from "../../styles/logged-stack/event-list.style";
import {
  ScrollView,
  Text,
  ActivityIndicator,
  View,
  TouchableOpacity,
  RefreshControl
} from "react-native";
import EventItem from "../../components/events-screen/event-item";
import EventCategories from "../../components/events-screen/event-categories";
import EventFilterDrawer, {
  EventFilters
} from "../../components/events-screen/event-filter-drawer";
import {useEvents, useEventCategories} from "../../hooks/api/use-events";
import {EventsListParams} from "../../services/api/events/events.service";
import stylesConstants from "../../styles/styles-constants";
import {Event, EventCategory} from "../../models/api/events.models";
import {PaymentEntity} from "../../models/api/payments.models";

const EventList: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedCategory, setSelectedCategory] = useState<
    EventCategory | undefined
  >(undefined);
  const [refreshing, setRefreshing] = useState(false);
  const [isFilterDrawerVisible, setIsFilterDrawerVisible] = useState(false);
  const [filters, setFilters] = useState<EventFilters>({
    eventTypes: [],
    isFree: null
  });

  // Parâmetros para buscar eventos
  const eventsParams: EventsListParams = useMemo(() => {
    const params: EventsListParams = {
      page: 1,
      pageSize: 20,
      search: searchTerm.trim() || undefined,
      category: selectedCategory?.id || undefined,
      sortBy: "date" as const,
      sortOrder: "asc" as const
      // Temporariamente removendo filtros avançados que podem estar causando erro
      // eventTypes: filters.eventTypes.length > 0 ? filters.eventTypes : undefined,
      // isFree: filters.isFree
    };

    // Remover propriedades undefined para evitar problemas na API
    Object.keys(params).forEach((key) => {
      if (params[key as keyof EventsListParams] === undefined) {
        delete params[key as keyof EventsListParams];
      }
    });

    // Log para debug dos filtros
    console.log("🔍 Filtros aplicados:", {
      search: params.search,
      category: params.category,
      hasSearch: !!params.search,
      hasCategory: !!params.category,
      finalParams: params
    });

    return params;
  }, [searchTerm, selectedCategory]);

  // Hooks para buscar dados
  const {
    data: eventsResponse,
    isLoading: isLoadingEvents,
    error: errorEvents,
    refetch: refetchEvents
  } = useEvents(eventsParams);

  // Debug dos eventos carregados
  console.log("📅 [EVENT-LIST] Estado dos eventos:", {
    isLoading: isLoadingEvents,
    hasData: !!eventsResponse,
    eventsCount: eventsResponse?.data?.length || 0,
    error: errorEvents?.message,
    firstEvent: eventsResponse?.data?.[0]
  });

  const {
    data: categories = [],
    isLoading: isLoadingCategories,
    error: errorCategories,
    refetch: refetchCategories
  } = useEventCategories();
  const handleSearchChange = useCallback(
    (value: string) => setSearchTerm(value),
    []
  );

  // Category selection handler with toggle functionality (same as products.tsx)
  const handleCategorySelect = useCallback(
    (category: EventCategory) => {
      // If the same category is selected, deselect it (toggle functionality)
      if (selectedCategory?.id === category.id) {
        setSelectedCategory(undefined);
      } else {
        setSelectedCategory(category);
      }
    },
    [selectedCategory]
  );

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Recarregar dados dos eventos
      await refetchEvents();
    } finally {
      setRefreshing(false);
    }
  }, [refetchEvents]);

  // Funções para lidar com filtros
  const handleFilterPress = useCallback(() => {
    setIsFilterDrawerVisible(true);
  }, []);

  const handleFilterClose = useCallback(() => {
    setIsFilterDrawerVisible(false);
  }, []);

  const handleFiltersChange = useCallback((newFilters: EventFilters) => {
    setFilters(newFilters);
  }, []);

  const handleApplyFilters = useCallback(() => {
    setIsFilterDrawerVisible(false);
    // Os filtros já estão aplicados através do useMemo dos eventsParams
  }, []);

  const handleClearFilters = useCallback(() => {
    setFilters({
      eventTypes: [],
      isFree: null
    });
    setIsFilterDrawerVisible(false);
  }, []);

  // Função para navegar para detalhes do evento
  const handleEventPress = useCallback(
    (event: any) => {
      console.log("🚀 Navegando para evento:", event?.id, event?.title);
      console.log(
        "🚀 URL de navegação:",
        `/(events)/event-sale?id=${event?.id}`
      );
      router.push(`/(events)/event-sale?id=${event?.id}`);
    },
    [router]
  );

  // Função para compra direta (anteriormente adicionar ao carrinho)
  const handleAddToCart = useCallback(
    (event: any) => {
      console.log("Compra direta:", event.title);
      // Navegar diretamente para a página de compra do evento
      router.push(`/(events)/event-sale?id=${event.id}`);
    },
    [router]
  );

  // Função para pagamento direto
  const handlePayNow = useCallback(
    (event: Event) => {
      console.log("💳 [EVENT-LIST] 🔧 TESTE FLUXO PAGAMENTO RESTAURADO");
      console.log("📋 [EVENT-LIST] Dados do evento:", {
        id: event.id,
        title: event.title,
        price: event.price,
        value: (event as any).value
      });

      // Calcula o preço do evento - API usa 'value' em vez de 'price'
      let amount = 0;
      if ((event as any).value) {
        amount = (event as any).value / 100; // Converte centavos para reais
        console.log("💰 [EVENT-LIST] Preço calculado via value:", amount);
      } else if (event.price?.amount) {
        amount = event.price.amount / 100; // Fallback para price.amount
        console.log(
          "💰 [EVENT-LIST] Preço calculado via price.amount:",
          amount
        );
      }

      const navigationParams = {
        eventId: event.id,
        entity: PaymentEntity.Event.toString(),
        amount: amount.toString()
      };

      console.log(
        "🧭 [EVENT-LIST] Navegando para pagamento com params:",
        navigationParams
      );

      // Navega para tela de pagamento
      router.push({
        pathname: "/(logged-stack)/payment",
        params: navigationParams
      });
    },
    [router]
  );

  return (
    <ScreenWithHeader
      screenTitle={t("eventList.title")}
      backButton
      disableScrollView={true}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 80}}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={stylesConstants.colors.primary500}
          />
        }
      >
        <Search
          searchBarValue={searchTerm}
          onSearchBarChange={handleSearchChange}
          onFilterPress={handleFilterPress}
          style={styles.search}
        />

        <EventCategories
          selectedCategory={selectedCategory}
          onCategorySelect={handleCategorySelect}
        />

        {/* Events section */}
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 8
          }}
        >
          <Text style={styles.sponsoredTitle}>
            {eventsResponse?.data.length
              ? `${eventsResponse.data.length} evento${
                  eventsResponse.data.length !== 1 ? "s" : ""
                } ${searchTerm || selectedCategory ? "encontrado" : ""}${
                  eventsResponse.data.length !== 1 &&
                  (searchTerm || selectedCategory)
                    ? "s"
                    : ""
                }`
              : t("eventList.sponsoredEvents", "Eventos em destaque")}
          </Text>

          {/* Indicador de filtros ativos - apenas para busca por texto */}
          {searchTerm && (
            <TouchableOpacity
              onPress={() => {
                setSearchTerm("");
                setSelectedCategory(undefined);
              }}
              style={{
                backgroundColor: stylesConstants.colors.primary100,
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 12,
                flexDirection: "row",
                alignItems: "center"
              }}
            >
              <Text
                style={{
                  fontSize: 12,
                  color: stylesConstants.colors.primary700,
                  marginRight: 4
                }}
              >
                Limpar filtros
              </Text>
              <Text
                style={{
                  fontSize: 10,
                  color: stylesConstants.colors.primary700
                }}
              >
                ✕
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Loading events */}
        {isLoadingEvents && (
          <View style={{padding: 20, alignItems: "center"}}>
            <ActivityIndicator
              size="large"
              color={stylesConstants.colors.primary500}
            />
            <Text
              style={{
                marginTop: 10,
                color: stylesConstants.colors.textSecondary
              }}
            >
              {t("eventList.loadingEvents", "Carregando eventos...")}
            </Text>
          </View>
        )}

        {/* Error state */}
        {errorEvents && !isLoadingEvents && (
          <View style={{padding: 20, alignItems: "center"}}>
            <Text
              style={{
                color: stylesConstants.colors.error500,
                textAlign: "center",
                marginBottom: 16
              }}
            >
              {t(
                "eventList.errorLoadingEvents",
                "Erro ao carregar eventos. Tente novamente."
              )}
            </Text>
            <TouchableOpacity
              onPress={handleRefresh}
              style={{
                backgroundColor: stylesConstants.colors.primary500,
                paddingHorizontal: 20,
                paddingVertical: 10,
                borderRadius: 8
              }}
            >
              <Text style={{color: "#FFFFFF", fontWeight: "600"}}>
                {t("eventList.retry", "Tentar Novamente")}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Events list */}
        {!isLoadingEvents && (eventsResponse?.data || errorEvents) && (
          <>
            {eventsResponse?.data && eventsResponse.data.length > 0 ? (
              <>
                {eventsResponse.data.map((event) => (
                  <View key={event.id} style={{marginBottom: 16}}>
                    <EventItem
                      event={event}
                      onPress={handleEventPress}
                      onAddToCart={handleAddToCart}
                      onPayNow={handlePayNow}
                    />
                  </View>
                ))}
              </>
            ) : (
              <View style={{padding: 20, alignItems: "center"}}>
                <Text
                  style={{
                    color: stylesConstants.colors.textSecondary,
                    textAlign: "center"
                  }}
                >
                  {searchTerm || selectedCategory
                    ? t(
                        "eventList.noEventsFound",
                        "Nenhum evento encontrado com os filtros aplicados."
                      )
                    : errorEvents
                    ? "Não foi possível carregar os eventos no momento."
                    : t(
                        "eventList.noEvents",
                        "Nenhum evento disponível no momento."
                      )}
                </Text>
              </View>
            )}
          </>
        )}
      </ScrollView>

      {/* Event Filter Drawer */}
      <EventFilterDrawer
        visible={isFilterDrawerVisible}
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onClose={handleFilterClose}
        onApply={handleApplyFilters}
        onClear={handleClearFilters}
      />
    </ScreenWithHeader>
  );
};

export default EventList;
