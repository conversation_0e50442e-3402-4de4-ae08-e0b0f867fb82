{"logs": [{"outputFile": "studio.takasaki.clubm.app-mergeDebugResources-66:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4adb0fa16e7e575806a9c770c8a059f4\\transformed\\biometric-1.2.0-alpha04\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,199,313,419,509,643,747,858,977,1106,1244,1371,1489,1620,1720,1868,1986,2112,2251,2371,2491,2584,2707,2789,2901,2997,3123", "endColumns": "143,113,105,89,133,103,110,118,128,137,126,117,130,99,147,117,125,138,119,119,92,122,81,111,95,125,95", "endOffsets": "194,308,414,504,638,742,853,972,1101,1239,1366,1484,1615,1715,1863,1981,2107,2246,2366,2486,2579,2702,2784,2896,2992,3118,3214"}, "to": {"startLines": "29,30,38,40,41,42,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2762,2906,3739,3943,4033,4167,4576,4687,4806,4935,5073,5200,5318,5449,5549,5697,5815,5941,6080,6200,6504,6597,6720,6802,6914,7010,7136", "endColumns": "143,113,105,89,133,103,110,118,128,137,126,117,130,99,147,117,125,138,119,119,92,122,81,111,95,125,95", "endOffsets": "2901,3015,3840,4028,4162,4266,4682,4801,4930,5068,5195,5313,5444,5544,5692,5810,5936,6075,6195,6315,6592,6715,6797,6909,7005,7131,7227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd17582311f056f52c6db3a5d3472b42\\transformed\\browser-1.6.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "39,43,44,45", "startColumns": "4,4,4,4", "startOffsets": "3845,4271,4368,4477", "endColumns": "97,96,108,98", "endOffsets": "3938,4363,4472,4571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9f1036362c92a9109f915df9f93bd0\\transformed\\core-1.13.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "31,32,33,34,35,36,37,61", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3020,3116,3218,3317,3416,3520,3623,6403", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3111,3213,3312,3411,3515,3618,3734,6499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e86979dddcd8eac9e9a65047af91df0a\\transformed\\appcompat-1.7.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,6320", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,6398"}}]}]}