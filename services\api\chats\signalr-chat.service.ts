/**
 * SignalR Chat Service for Club M
 * Provides real-time messaging functionality using SignalR
 */

import * as signalR from "@microsoft/signalr";
import {ApiLogger} from "../base/api-logger";
import {
  SafeChatMessageViewModel,
  SignalRChatEvents,
  SignalRConnectionState
} from "@/models/api/chats-api.models";

export interface SignalRChatServiceConfig {
  hubUrl: string;
  accessTokenFactory: () => string | Promise<string>;
  automaticReconnect?: boolean;
  logLevel?: signalR.LogLevel;
}

export interface SignalREventHandlers {
  onMessageReceived?: (message: SafeChatMessageViewModel) => void;
  onTypingIndicator?: (data: SignalRChatEvents["TypingIndicator"]) => void;
  onMessageStatusUpdate?: (
    data: SignalRChatEvents["MessageStatusUpdate"]
  ) => void;
  onUserJoined?: (data: SignalRChatEvents["UserJoined"]) => void;
  onUserLeft?: (data: SignalRChatEvents["UserLeft"]) => void;
  onUserOnlineStatusChanged?: (
    data: SignalRChatEvents["UserOnlineStatusChanged"]
  ) => void;
  onConnectionStateChanged?: (state: SignalRConnectionState) => void;
}

export class SignalRChatService {
  private connection: signalR.HubConnection | null = null;
  private config: SignalRChatServiceConfig;
  private eventHandlers: SignalREventHandlers = {};
  private connectionState: SignalRConnectionState = {
    status: "disconnected",
    reconnectAttempts: 0,
    maxReconnectAttempts: 5
  };
  private joinedChats: Set<number> = new Set();

  constructor(config: SignalRChatServiceConfig) {
    this.config = {
      automaticReconnect: true,
      logLevel: signalR.LogLevel.Information,
      ...config
    };
  }

  /**
   * Initialize the SignalR connection
   */
  public async initialize(): Promise<void> {
    try {
      ApiLogger.info("Initializing SignalR connection", {
        hubUrl: this.config.hubUrl
      });

      // Build the connection
      const builder = new signalR.HubConnectionBuilder()
        .withUrl(this.config.hubUrl, {
          accessTokenFactory: async () => {
            try {
              const token = await this.config.accessTokenFactory();
              ApiLogger.info("SignalR accessTokenFactory called", {
                hasToken: !!token,
                tokenLength: token?.length || 0,
                tokenPrefix: token?.substring(0, 10) + "..."
              });
              return token;
            } catch (error) {
              ApiLogger.error(
                "SignalR accessTokenFactory failed",
                error as Error
              );
              throw error;
            }
          },
          // Configure transport options for better compatibility
          transport:
            signalR.HttpTransportType.WebSockets |
            signalR.HttpTransportType.LongPolling,
          // Add headers for debugging
          headers: {
            "User-Agent": "ClubM-Mobile-App"
          },
          // Skip negotiation for WebSockets (can help with some proxy issues)
          skipNegotiation: false,
          // Timeout settings
          timeout: 30000
        })
        .configureLogging(this.config.logLevel!);

      // Add automatic reconnect if enabled
      if (this.config.automaticReconnect) {
        builder.withAutomaticReconnect([0, 2000, 10000, 30000]);
      }

      this.connection = builder.build();

      // Set up event handlers
      this.setupEventHandlers();
      this.setupConnectionEvents();

      ApiLogger.info("SignalR connection initialized successfully");
    } catch (error) {
      ApiLogger.error(
        "Failed to initialize SignalR connection",
        error as Error
      );
      this.updateConnectionState({
        status: "error",
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Test connectivity to the SignalR endpoint
   */
  private async testConnectivity(): Promise<boolean> {
    try {
      // Try to make a simple HTTP request to the base URL to test connectivity
      const baseUrl = this.config.hubUrl.replace("/hubs/chat", "");
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${baseUrl}/health`, {
        method: "GET",
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      ApiLogger.warn("Connectivity test failed", error as Error);
      return false;
    }
  }

  /**
   * Connect to the SignalR hub
   */
  public async connect(): Promise<void> {
    if (!this.connection) {
      throw new Error(
        "SignalR connection not initialized. Call initialize() first."
      );
    }

    // Check if already connected
    if (this.connection.state === signalR.HubConnectionState.Connected) {
      ApiLogger.warn("SignalR connection already established");
      return;
    }

    // Check if already connecting or reconnecting
    if (
      this.connection.state === signalR.HubConnectionState.Connecting ||
      this.connection.state === signalR.HubConnectionState.Reconnecting
    ) {
      ApiLogger.warn("SignalR connection already in progress", {
        currentState: this.connection.state
      });
      return;
    }

    // If connection is in an invalid state, stop it first
    if (this.connection.state !== signalR.HubConnectionState.Disconnected) {
      ApiLogger.info("Stopping existing connection before reconnecting", {
        currentState: this.connection.state
      });
      try {
        await this.connection.stop();
      } catch (stopError) {
        ApiLogger.warn(
          "Error stopping existing connection",
          stopError as Error
        );
      }
    }

    try {
      this.updateConnectionState({status: "connecting"});
      ApiLogger.info("Connecting to SignalR hub...", {
        hubUrl: this.config.hubUrl,
        currentState: this.connection.state
      });

      await this.connection.start();

      ApiLogger.info("SignalR connection started, invoking ListenChats...");

      // Invoke ListenChats as shown in the example
      await this.connection.invoke("ListenChats");

      this.updateConnectionState({
        status: "connected",
        lastConnected: new Date(),
        reconnectAttempts: 0
      });

      ApiLogger.info("SignalR connection established successfully", {
        connectionId: this.connection.connectionId,
        state: this.connection.state
      });

      // Rejoin previously joined chats
      await this.rejoinChats();
    } catch (error) {
      const errorMessage = (error as Error).message;
      ApiLogger.error("Failed to connect to SignalR hub", error as Error, {
        hubUrl: this.config.hubUrl,
        connectionState: this.connection?.state,
        errorType: (error as Error).constructor.name
      });

      this.updateConnectionState({
        status: "error",
        error: errorMessage,
        reconnectAttempts: this.connectionState.reconnectAttempts + 1
      });
      throw error;
    }
  }

  /**
   * Disconnect from the SignalR hub
   */
  public async disconnect(): Promise<void> {
    if (!this.connection) {
      return;
    }

    try {
      ApiLogger.info("Disconnecting from SignalR hub...");

      // Leave all joined chats before disconnecting
      for (const chatId of this.joinedChats) {
        await this.leaveChat(chatId);
      }

      await this.connection.stop();
      this.updateConnectionState({status: "disconnected"});

      ApiLogger.info("SignalR connection closed");
    } catch (error) {
      ApiLogger.error("Error during SignalR disconnection", error as Error);
      throw error;
    }
  }

  /**
   * Join a specific chat room
   */
  public async joinChat(chatId: number): Promise<void> {
    if (!this.isConnected()) {
      throw new Error("SignalR connection not established");
    }

    try {
      ApiLogger.info("Joining chat", {chatId});

      await this.connection!.invoke("JoinChat", chatId);
      this.joinedChats.add(chatId);

      ApiLogger.info("Successfully joined chat", {chatId});
    } catch (error) {
      ApiLogger.error("Failed to join chat", error as Error, {chatId});
      throw error;
    }
  }

  /**
   * Leave a specific chat room
   */
  public async leaveChat(chatId: number): Promise<void> {
    if (!this.isConnected()) {
      return;
    }

    try {
      ApiLogger.info("Leaving chat", {chatId});

      await this.connection!.invoke("LeaveChat", chatId);
      this.joinedChats.delete(chatId);

      ApiLogger.info("Successfully left chat", {chatId});
    } catch (error) {
      ApiLogger.error("Failed to leave chat", error as Error, {chatId});
      throw error;
    }
  }

  /**
   * Send a message through SignalR
   */
  public async sendMessage(chatId: number, message: string): Promise<void> {
    if (!this.isConnected()) {
      throw new Error("SignalR connection not established");
    }

    try {
      ApiLogger.info("Sending message via SignalR", {
        chatId,
        messageLength: message.length
      });

      await this.connection!.invoke("SendMessage", chatId, message);

      ApiLogger.info("Message sent successfully via SignalR", {chatId});
    } catch (error) {
      ApiLogger.error("Failed to send message via SignalR", error as Error, {
        chatId
      });
      throw error;
    }
  }

  /**
   * Send typing indicator
   */
  public async sendTypingIndicator(
    chatId: number,
    isTyping: boolean
  ): Promise<void> {
    if (!this.isConnected()) {
      return;
    }

    try {
      await this.connection!.invoke("SendTypingIndicator", chatId, isTyping);
    } catch (error) {
      ApiLogger.error("Failed to send typing indicator", error as Error, {
        chatId,
        isTyping
      });
    }
  }

  /**
   * Mark message as read
   */
  public async markMessageAsRead(messageId: number): Promise<void> {
    if (!this.isConnected()) {
      return;
    }

    try {
      await this.connection!.invoke("MarkMessageAsRead", messageId);
    } catch (error) {
      ApiLogger.error("Failed to mark message as read", error as Error, {
        messageId
      });
    }
  }

  /**
   * Check if connection is established
   */
  public isConnected(): boolean {
    return this.connection?.state === signalR.HubConnectionState.Connected;
  }

  /**
   * Get current connection state
   */
  public getConnectionState(): SignalRConnectionState {
    return {...this.connectionState};
  }

  /**
   * Set event handlers
   */
  public setEventHandlers(handlers: SignalREventHandlers): void {
    this.eventHandlers = {...this.eventHandlers, ...handlers};
  }

  /**
   * Remove event handlers
   */
  public removeEventHandlers(): void {
    this.eventHandlers = {};
  }

  /**
   * Setup SignalR event handlers
   */
  private setupEventHandlers(): void {
    if (!this.connection) return;

    // Message received
    this.connection.on("SendMessage", (data: SafeChatMessageViewModel) => {
      ApiLogger.info("Message received via SignalR", {messageId: data.id});
      this.eventHandlers.onMessageReceived?.(data);
    });

    // Typing indicator
    this.connection.on(
      "TypingIndicator",
      (data: SignalRChatEvents["TypingIndicator"]) => {
        this.eventHandlers.onTypingIndicator?.(data);
      }
    );

    // Message status update
    this.connection.on(
      "MessageStatusUpdate",
      (data: SignalRChatEvents["MessageStatusUpdate"]) => {
        this.eventHandlers.onMessageStatusUpdate?.(data);
      }
    );

    // User joined
    this.connection.on(
      "UserJoined",
      (data: SignalRChatEvents["UserJoined"]) => {
        this.eventHandlers.onUserJoined?.(data);
      }
    );

    // User left
    this.connection.on("UserLeft", (data: SignalRChatEvents["UserLeft"]) => {
      this.eventHandlers.onUserLeft?.(data);
    });

    // User online status changed
    this.connection.on(
      "UserOnlineStatusChanged",
      (data: SignalRChatEvents["UserOnlineStatusChanged"]) => {
        this.eventHandlers.onUserOnlineStatusChanged?.(data);
      }
    );
  }

  /**
   * Setup connection state event handlers
   */
  private setupConnectionEvents(): void {
    if (!this.connection) return;

    this.connection.onreconnecting((error) => {
      ApiLogger.warn("SignalR reconnecting...", {error: error?.message});
      this.updateConnectionState({
        status: "reconnecting",
        error: error?.message,
        reconnectAttempts: this.connectionState.reconnectAttempts + 1
      });
    });

    this.connection.onreconnected((connectionId) => {
      ApiLogger.info("SignalR reconnected", {connectionId});
      this.updateConnectionState({
        status: "connected",
        lastConnected: new Date(),
        error: undefined
      });

      // Rejoin chats after reconnection
      this.rejoinChats();
    });

    this.connection.onclose((error) => {
      ApiLogger.warn("SignalR connection closed", {
        error: error?.message,
        errorType: error?.constructor?.name,
        connectionId: this.connection?.connectionId
      });

      // Log additional details if it's an authentication error
      if (
        error?.message?.includes("401") ||
        error?.message?.includes("Unauthorized")
      ) {
        ApiLogger.error(
          "SignalR connection closed due to authentication error",
          error as Error,
          {
            hubUrl: this.config.hubUrl
          }
        );
      }

      this.updateConnectionState({
        status: "disconnected",
        error: error?.message
      });
    });
  }

  /**
   * Update connection state and notify handlers
   */
  private updateConnectionState(
    updates: Partial<SignalRConnectionState>
  ): void {
    this.connectionState = {...this.connectionState, ...updates};
    this.eventHandlers.onConnectionStateChanged?.(this.connectionState);
  }

  /**
   * Rejoin previously joined chats after reconnection
   */
  private async rejoinChats(): Promise<void> {
    for (const chatId of this.joinedChats) {
      try {
        await this.joinChat(chatId);
      } catch (error) {
        ApiLogger.error(
          "Failed to rejoin chat after reconnection",
          error as Error,
          {chatId}
        );
      }
    }
  }
}

export default SignalRChatService;
