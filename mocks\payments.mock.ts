/**
 * Mocks MSW para API de pagamentos
 * Simula comportamento da API real para desenvolvimento
 */

import {http, HttpResponse} from "msw";
import {
  Payment,
  CreditCard,
  PaymentStatus,
  PaymentType,
  PaymentEntity,
  CreatePaymentRequest,
  CreateCreditCardRequest
} from "../models/api/payments.models";

// Dados mock para cartões de crédito
const mockCreditCards: CreditCard[] = [
  {
    id: "1",
    holderName: "JOÃO DA SILVA",
    expirationMonth: 12,
    expirationYear: 2025,
    brand: "Visa",
    lastFourDigits: "1234",
    isDefault: true,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  {
    id: "2",
    holderName: "MARIA SANTOS",
    expirationMonth: 6,
    expirationYear: 2026,
    brand: "Mastercard",
    lastFourDigits: "5678",
    isDefault: false,
    createdAt: "2024-01-02T00:00:00Z",
    updatedAt: "2024-01-02T00:00:00Z"
  }
];

// Dados mock para pagamentos
const mockPayments: Payment[] = [
  {
    id: "payment-1",
    entity: PaymentEntity.Event,
    entityId: 1,
    type: PaymentType.CreditCard,
    amount: 150.0,
    status: PaymentStatus.Confirmed,
    userId: "user-1",
    transactionId: "txn-123",
    createdAt: "2024-01-01T10:00:00Z",
    updatedAt: "2024-01-01T10:05:00Z",
    paidAt: "2024-01-01T10:05:00Z",
    creditCard: {
      creditCardId: 1,
      installmentCount: 1
    }
  },
  {
    id: "payment-2",
    entity: PaymentEntity.Event,
    entityId: 2,
    type: PaymentType.Pix,
    amount: 75.5,
    status: PaymentStatus.Pending,
    userId: "user-1",
    pixQrCode:
      "00020126580014br.gov.bcb.pix0136123e4567-e12b-12d1-a456-426614174000520400005303986540475.505802BR5913EMPRESA TESTE6009SAO PAULO62070503***6304ABCD",
    pixQrCodeBase64:
      "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
    createdAt: "2024-01-02T14:30:00Z",
    updatedAt: "2024-01-02T14:30:00Z"
  },
  {
    id: "payment-3",
    entity: PaymentEntity.Product,
    entityId: 5,
    type: PaymentType.Boleto,
    amount: 200.0,
    status: PaymentStatus.Created,
    userId: "user-1",
    boletoUrl: "https://example.com/boleto/payment-3.pdf",
    boletoBarcode: "23793.39126 60000.000000 00000.000000 1 84370000020000",
    expiresAt: "2024-01-10T23:59:59Z",
    createdAt: "2024-01-03T09:15:00Z",
    updatedAt: "2024-01-03T09:15:00Z",
    boleto: {
      installmentCount: 1
    }
  }
];

// Handlers MSW
export const paymentsHandlers = [
  // Listar pagamentos
  http.get("/api/app/payments", ({request}) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "10");
    const status = url.searchParams.get("status");
    const entity = url.searchParams.get("entity");
    const entityId = url.searchParams.get("entityId");

    console.log("🔍 [MSW-PAYMENTS] Listando pagamentos:", {
      page,
      pageSize,
      status,
      entity,
      entityId
    });

    let filteredPayments = [...mockPayments];

    // Aplicar filtros
    if (status) {
      filteredPayments = filteredPayments.filter((p) => p.status === status);
    }
    if (entity) {
      filteredPayments = filteredPayments.filter(
        (p) => p.entity === parseInt(entity)
      );
    }
    if (entityId) {
      filteredPayments = filteredPayments.filter(
        (p) => p.entityId === parseInt(entityId)
      );
    }

    // Paginação
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedPayments = filteredPayments.slice(startIndex, endIndex);

    return HttpResponse.json(paginatedPayments);
  }),

  // Buscar pagamento por ID
  http.get("/api/app/payments/:id", ({params}) => {
    const {id} = params;
    console.log("🔍 [MSW-PAYMENTS] Buscando pagamento:", id);

    const payment = mockPayments.find((p) => p.id === id);
    if (!payment) {
      return new HttpResponse(null, {status: 404});
    }

    return HttpResponse.json(payment);
  }),

  // Criar pagamento
  http.post("/api/app/payments", async ({request}) => {
    const paymentData = (await request.json()) as CreatePaymentRequest;
    console.log("💳 [MSW-PAYMENTS] Criando pagamento:", paymentData);

    const newPayment: Payment = {
      id: `payment-${Date.now()}`,
      entity: paymentData.entity,
      entityId: paymentData.entityId,
      type: paymentData.type,
      amount: 100.0, // Valor mock
      status: PaymentStatus.Created,
      userId: "user-1",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...(paymentData.creditCard && {creditCard: paymentData.creditCard}),
      ...(paymentData.boleto && {boleto: paymentData.boleto})
    };

    // Simular diferentes comportamentos baseados no tipo
    if (paymentData.type === PaymentType.Pix) {
      newPayment.pixQrCode =
        "00020126580014br.gov.bcb.pix0136123e4567-e12b-12d1-a456-426614174000520400005303986540475.505802BR5913EMPRESA TESTE6009SAO PAULO62070503***6304ABCD";
      newPayment.pixQrCodeBase64 =
        "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
      newPayment.status = PaymentStatus.Pending;
    } else if (paymentData.type === PaymentType.Boleto) {
      newPayment.boletoUrl = `https://example.com/boleto/${newPayment.id}.pdf`;
      newPayment.boletoBarcode =
        "23793.39126 60000.000000 00000.000000 1 84370000020000";
      newPayment.expiresAt = new Date(
        Date.now() + 7 * 24 * 60 * 60 * 1000
      ).toISOString(); // 7 dias
    } else if (paymentData.type === PaymentType.CreditCard) {
      newPayment.status = PaymentStatus.Confirmed;
      newPayment.paidAt = new Date().toISOString();
      newPayment.transactionId = `txn-${Date.now()}`;
    }

    mockPayments.push(newPayment);
    return HttpResponse.json(newPayment, {status: 201});
  }),

  // QR Code do pagamento
  http.get("/api/app/payments/:id/qr-code", ({params}) => {
    const {id} = params;
    console.log("📱 [MSW-PAYMENTS] Buscando QR Code:", id);

    const payment = mockPayments.find((p) => p.id === id);
    if (!payment || !payment.pixQrCodeBase64) {
      return new HttpResponse(null, {status: 404});
    }

    return HttpResponse.json(payment.pixQrCodeBase64);
  }),

  // Código de barras do pagamento
  http.get("/api/app/payments/:id/bar-code", ({params}) => {
    const {id} = params;
    console.log("📊 [MSW-PAYMENTS] Buscando código de barras:", id);

    const payment = mockPayments.find((p) => p.id === id);
    if (!payment || !payment.boletoBarcode) {
      return new HttpResponse(null, {status: 404});
    }

    return HttpResponse.json(payment.boletoBarcode);
  }),

  // Listar cartões de crédito
  http.get("/api/app/creditCards", () => {
    console.log("💳 [MSW-PAYMENTS] Listando cartões de crédito");
    return HttpResponse.json(mockCreditCards);
  }),

  // Buscar cartão por ID
  http.get("/api/app/creditCards/:id", ({params}) => {
    const {id} = params;
    console.log("💳 [MSW-PAYMENTS] Buscando cartão:", id);

    const card = mockCreditCards.find((c) => c.id === id);
    if (!card) {
      return new HttpResponse(null, {status: 404});
    }

    return HttpResponse.json(card);
  }),

  // Criar cartão de crédito
  http.post("/api/app/creditCards", async ({request}) => {
    const cardData = (await request.json()) as CreateCreditCardRequest;
    console.log("💳 [MSW-PAYMENTS] Criando cartão:", {
      holderName: cardData.holderName,
      lastFourDigits: cardData.number.slice(-4)
    });

    const newCard: CreditCard = {
      id: `card-${Date.now()}`,
      holderName: cardData.holderName,
      expirationMonth: parseInt(cardData.expiryMonth),
      expirationYear: parseInt(cardData.expiryYear),
      brand: cardData.number.startsWith("4") ? "Visa" : "Mastercard",
      lastFourDigits: cardData.number.slice(-4),
      isDefault: mockCreditCards.length === 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    mockCreditCards.push(newCard);
    return HttpResponse.json(newCard, {status: 201});
  }),

  // Deletar cartão de crédito
  http.delete("/api/app/creditCards/:id", ({params}) => {
    const {id} = params;
    console.log("🗑️ [MSW-PAYMENTS] Deletando cartão:", id);

    const index = mockCreditCards.findIndex((c) => c.id === id);
    if (index === -1) {
      return new HttpResponse(null, {status: 404});
    }

    mockCreditCards.splice(index, 1);
    return new HttpResponse(null, {status: 204});
  })
];
