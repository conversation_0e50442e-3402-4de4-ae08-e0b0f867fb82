/**
 * Tela de pagamento principal
 * Integra todos os componentes do fluxo de pagamento
 */

import React, {useCallback, useState, useMemo} from "react";
import {View, ScrollView, Alert, Text} from "react-native";
import {useTranslation} from "react-i18next";
import {useLocalSearchParams, useRouter} from "expo-router";
import {SafeAreaView} from "react-native-safe-area-context";

import PaymentMethodSelector from "../../components/payment/payment-method-selector";
import CreditCardForm from "../../components/payment/credit-card-form";
import PaymentSummary, {
  PaymentSummaryItem
} from "../../components/payment/payment-summary";
import Button from "../../components/button";
import LoadingOverlay from "../../components/loading-overlay";

import {
  PaymentType,
  CreditCard,
  PaymentEntity,
  CreatePaymentRequest,
  CreateCreditCardFormData
} from "../../models/api/payments.models";

import {
  useCreatePayment,
  useCreditCards,
  useCreateCreditCard
} from "../../hooks/api/use-payments";

import {useEvent} from "../../hooks/api/use-events";
import {useProduct} from "../../hooks/api/use-products";
import styles from "../../styles/screens/payment.style";

const PaymentScreen: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();

  // Parâmetros da URL
  const eventId = parseInt(params.eventId as string);
  const entity =
    (parseInt(params.entity as string) as PaymentEntity) || PaymentEntity.Event;
  const amount = parseFloat(params.amount as string) || 0;

  console.log("🎯 [PAYMENT] Tela de pagamento iniciada");
  console.log("📋 [PAYMENT] Parâmetros recebidos:", {
    eventId,
    entity,
    amount,
    allParams: params
  });

  // Estados locais
  const [selectedMethod, setSelectedMethod] = useState<PaymentType>();
  const [selectedSavedCard, setSelectedSavedCard] = useState<CreditCard>();
  const [creditCardData, setCreditCardData] =
    useState<CreateCreditCardFormData>();
  const [step, setStep] = useState<"method" | "details" | "summary">("method");
  const [showNewCardForm, setShowNewCardForm] = useState(false);

  // Hooks de API
  const {
    data: event,
    isLoading: isLoadingEvent,
    error: eventError
  } = useEvent(eventId.toString());
  const {
    data: creditCards = [],
    isLoading: isLoadingCreditCards,
    error: creditCardsError
  } = useCreditCards();
  const createPaymentMutation = useCreatePayment();
  const createCreditCardMutation = useCreateCreditCard();

  // Debug dos cartões carregados
  console.log("💳 [PAYMENT] Estado dos cartões:", {
    isLoading: isLoadingCreditCards,
    hasCards: creditCards.length > 0,
    cardsCount: creditCards.length,
    error: creditCardsError?.message,
    firstCard: creditCards[0]
  });

  // Debug do estado de seleção de cartão
  React.useEffect(() => {
    console.log(
      "🔄 [PAYMENT] 🔧 DEBUG ESTADO - Mudança no cartão selecionado:",
      {
        selectedSavedCardId: selectedSavedCard?.id,
        selectedSavedCardBrand: selectedSavedCard?.brand,
        selectedMethod,
        step,
        showNewCardForm
      }
    );
  }, [selectedSavedCard, selectedMethod, step, showNewCardForm]);

  console.log("📋 [PAYMENT] Estado do evento:", {
    eventId,
    isLoadingEvent,
    hasEvent: !!event,
    eventError: eventError?.message,
    eventTitle: event?.title,
    eventPrice: event?.price
  });

  // Dados do item para pagamento
  const paymentItem: PaymentSummaryItem = useMemo(() => {
    if (event) {
      console.log("📋 [PAYMENT] Mapeando dados do evento:", {
        eventName: event.title,
        eventTitle: event.title,
        eventValue: (event as any).value,
        eventPrice: event.price,
        paramAmount: amount
      });

      return {
        id: event.id,
        title: event.title || t("payment.defaultItemTitle"),
        description: event.description,
        imageUrl: event.images?.[0]?.url,
        price: (event as any).value ? (event as any).value / 100 : amount,
        quantity: 1,
        entity: PaymentEntity.Event
      };
    }
    return {
      id: String(eventId),
      title: t("payment.defaultItemTitle"),
      price: amount,
      quantity: 1,
      entity
    };
  }, [event, eventId, amount, entity, t]);

  // Verifica se pode prosseguir para próximo passo
  const canProceedToDetails = useMemo(() => {
    const canProceed = selectedMethod !== undefined;

    console.log("🔍 [PAYMENT] Verificando canProceedToDetails:", {
      selectedMethod,
      canProceed
    });

    return canProceed;
  }, [selectedMethod]);

  const canProceedToSummary = useMemo(() => {
    console.log(
      "🔍 [PAYMENT] 🔧 CORREÇÃO NAVEGAÇÃO - Verificando canProceedToSummary:",
      {
        selectedMethod,
        selectedSavedCard: !!selectedSavedCard,
        creditCardData: !!creditCardData,
        canProceedToDetails
      }
    );

    // Se é cartão de crédito
    if (selectedMethod === PaymentType.CreditCard) {
      // Se tem cartão salvo selecionado, pode prosseguir para resumo
      if (selectedSavedCard) {
        console.log(
          "✅ [PAYMENT] 🔧 CORREÇÃO NAVEGAÇÃO - Cartão salvo selecionado, pode ir para resumo"
        );
        return true;
      }
      // Se não tem cartão salvo, precisa ter dados do novo cartão
      if (!selectedSavedCard) {
        const canProceed = creditCardData !== undefined;
        console.log(
          "🔍 [PAYMENT] 🔧 CORREÇÃO NAVEGAÇÃO - Novo cartão, precisa de dados:",
          canProceed
        );
        return canProceed;
      }
    }

    // Para outros métodos (PIX, Boleto)
    const canProceed = canProceedToDetails;
    console.log(
      "🔍 [PAYMENT] 🔧 CORREÇÃO NAVEGAÇÃO - Outros métodos:",
      canProceed
    );
    return canProceed;
  }, [selectedMethod, selectedSavedCard, creditCardData, canProceedToDetails]);

  // Handlers
  const handleMethodSelect = useCallback(
    (method: PaymentType, preserveSelectedCard = false) => {
      console.log(
        "💳 [PAYMENT] 🔧 TESTE FLUXO - Método de pagamento selecionado:",
        {
          method,
          methodName:
            method === PaymentType.CreditCard
              ? "CreditCard"
              : method === PaymentType.Pix
              ? "Pix"
              : method === PaymentType.Boleto
              ? "Boleto"
              : "Unknown",
          creditCardsCount: creditCards.length,
          hasCreditCards: creditCards.length > 0,
          preserveSelectedCard
        }
      );

      setSelectedMethod(method);

      // Só limpa o cartão selecionado se não for para preservar
      if (!preserveSelectedCard) {
        setSelectedSavedCard(undefined);
      }

      setCreditCardData(undefined);
      setShowNewCardForm(false);

      // Se selecionou cartão de crédito e não há cartões salvos, vai direto para formulário
      if (method === PaymentType.CreditCard && creditCards.length === 0) {
        console.log(
          "🆕 [PAYMENT] Sem cartões salvos, indo direto para formulário"
        );
        setShowNewCardForm(true);
      } else if (method === PaymentType.CreditCard && creditCards.length > 0) {
        console.log(
          "📋 [PAYMENT] Há cartões salvos, usuário deve escolher entre eles ou adicionar novo"
        );
      }
    },
    [creditCards.length]
  );

  const handleSavedCardSelect = useCallback(
    (card: CreditCard) => {
      console.log(
        "💾 [PAYMENT] 🔧 CORREÇÃO SELEÇÃO - Cartão salvo selecionado:",
        {
          id: card.id,
          brand: card.brand,
          lastFour: card.lastFourDigits || card.number?.slice(-4),
          holderName: card.holderName
        }
      );

      console.log("💾 [PAYMENT] 🔧 DEBUG - Estado ANTES da seleção:", {
        selectedSavedCard: selectedSavedCard?.id,
        selectedMethod,
        showNewCardForm
      });

      setSelectedSavedCard(card);
      setSelectedMethod(PaymentType.CreditCard);
      setCreditCardData(undefined);
      setShowNewCardForm(false);

      // Log imediato após setState (pode não refletir mudança ainda)
      console.log("💾 [PAYMENT] 🔧 CORREÇÃO SELEÇÃO - Estado após seleção:", {
        selectedSavedCard: !!card,
        selectedMethod: PaymentType.CreditCard,
        showNewCardForm: false,
        readyForSummary: true
      });
    },
    [selectedSavedCard, selectedMethod, showNewCardForm]
  );

  const handleAddNewCard = useCallback(() => {
    console.log(
      "🆕 [PAYMENT] 🔧 NAVEGAÇÃO DIRETA - Usuário quer adicionar novo cartão"
    );
    setSelectedSavedCard(undefined);
    setCreditCardData(undefined);
    setShowNewCardForm(true);
    setSelectedMethod(PaymentType.CreditCard);
    setStep("details"); // Navega diretamente para o formulário

    console.log(
      "🆕 [PAYMENT] 🔧 NAVEGAÇÃO DIRETA - Navegando direto para formulário:",
      {
        step: "details",
        showNewCardForm: true,
        selectedSavedCard: null,
        selectedMethod: PaymentType.CreditCard,
        creditCardsCount: creditCards.length
      }
    );
  }, [creditCards.length]);

  const handleBackToMethodSelection = useCallback(() => {
    console.log("🔙 [PAYMENT] 🔧 NAVEGAÇÃO - Voltando para seleção de método");
    setStep("method");
    setShowNewCardForm(false);
    setSelectedSavedCard(undefined);
    setCreditCardData(undefined);

    console.log("🔙 [PAYMENT] 🔧 NAVEGAÇÃO - Estado após voltar:", {
      step: "method",
      showNewCardForm: false,
      selectedSavedCard: null,
      selectedMethod: selectedMethod
    });
  }, [selectedMethod]);

  const handleCreditCardSubmit = useCallback(
    async (data: CreateCreditCardFormData) => {
      console.log("💳 [PAYMENT] Dados do cartão submetidos:", {
        holderName: data.holderName,
        cardNumber: data.number.slice(-4), // Só os últimos 4 dígitos por segurança
        expiryMonth: data.expiryMonth,
        expiryYear: data.expiryYear
      });

      try {
        // Cria o cartão primeiro
        const newCardResponse = await createCreditCardMutation.mutateAsync(
          data
        );
        const newCard = newCardResponse.data;
        console.log("✅ [PAYMENT] Cartão criado com sucesso:", newCard.id);

        // Define como cartão selecionado
        setSelectedSavedCard(newCard);
        setCreditCardData(data);
        setStep("summary");
      } catch (error) {
        console.error("❌ [PAYMENT] Erro ao criar cartão:", error);
        // O erro já é tratado pelo hook
      }
    },
    [createCreditCardMutation]
  );

  const handleNextStep = useCallback(() => {
    console.log("➡️ [PAYMENT] Avançando para próximo passo");
    console.log("📊 [PAYMENT] Estado atual:", {
      step,
      selectedMethod,
      selectedSavedCard: !!selectedSavedCard,
      canProceedToDetails,
      canProceedToSummary
    });

    if (step === "method") {
      if (selectedMethod === PaymentType.CreditCard) {
        // Se selecionou um cartão salvo, vai direto para resumo
        if (selectedSavedCard) {
          console.log(
            "✅ [PAYMENT] 🔧 CORREÇÃO - Cartão salvo selecionado, indo DIRETO para resumo:",
            {
              cardId: selectedSavedCard.id,
              cardBrand: selectedSavedCard.brand,
              cardLastFour:
                selectedSavedCard.lastFourDigits ||
                selectedSavedCard.number?.slice(-4)
            }
          );
          setStep("summary");
          return;
        }

        // Se quer adicionar novo cartão ou não tem cartões salvos
        if (showNewCardForm || creditCards.length === 0) {
          console.log("🆕 [PAYMENT] Indo para formulário de novo cartão");
          setStep("details");
          return;
        }

        // Se tem cartões salvos mas não selecionou nenhum
        console.log(
          "❌ [PAYMENT] Precisa selecionar um cartão ou adicionar novo"
        );
        return;
      } else {
        // PIX ou Boleto - vai direto para resumo
        console.log(
          "🔄 [PAYMENT] Método não-cartão selecionado, indo para resumo"
        );
        setStep("summary");
      }
    } else if (step === "details" && canProceedToSummary) {
      console.log("🔄 [PAYMENT] Formulário preenchido, indo para resumo");
      setStep("summary");
    }
  }, [
    step,
    selectedMethod,
    selectedSavedCard,
    showNewCardForm,
    creditCards.length,
    canProceedToSummary
  ]);

  const handlePreviousStep = useCallback(() => {
    if (step === "summary") {
      if (selectedMethod === PaymentType.CreditCard && !selectedSavedCard) {
        setStep("details");
      } else {
        setStep("method");
      }
    } else if (step === "details") {
      setStep("method");
    }
  }, [step, selectedMethod, selectedSavedCard]);

  const handlePayment = useCallback(async () => {
    console.log("🔍 [PAYMENT] 🔧 DEBUG VALIDAÇÃO - Verificando método:", {
      selectedMethod,
      selectedMethodType: typeof selectedMethod,
      isUndefined: selectedMethod === undefined,
      isNull: selectedMethod === null,
      isFalsy: !selectedMethod,
      PaymentTypeCreditCard: PaymentType.CreditCard,
      isEqualToCreditCard: selectedMethod === PaymentType.CreditCard
    });

    if (selectedMethod === undefined || selectedMethod === null) {
      console.log("❌ [PAYMENT] Nenhum método selecionado");
      Alert.alert(t("payment.error.title"), t("payment.error.noMethod"), [
        {text: t("common.ok")}
      ]);
      return;
    }

    // Validação específica para cartão de crédito
    if (selectedMethod === PaymentType.CreditCard && !selectedSavedCard) {
      console.log(
        "❌ [PAYMENT] Cartão de crédito selecionado mas nenhum cartão escolhido"
      );
      Alert.alert(t("payment.error.title"), t("payment.error.noCard"), [
        {text: t("common.ok")}
      ]);
      return;
    }

    console.log("🚀 [PAYMENT] Iniciando processamento do pagamento");
    console.log("📋 [PAYMENT] Dados do pagamento:", {
      selectedMethod,
      methodName:
        selectedMethod === PaymentType.CreditCard
          ? "CreditCard"
          : selectedMethod === PaymentType.Pix
          ? "Pix"
          : selectedMethod === PaymentType.Boleto
          ? "Boleto"
          : "Unknown",
      entity,
      eventId,
      amount,
      selectedSavedCard: !!selectedSavedCard,
      selectedSavedCardId: selectedSavedCard?.id,
      paymentItemTitle: paymentItem.title
    });

    try {
      const paymentData: CreatePaymentRequest = {
        entity,
        entityId: eventId,
        type: selectedMethod
      };

      // Adiciona dados específicos do método de pagamento
      const method = selectedMethod as PaymentType;
      if (method === PaymentType.CreditCard && selectedSavedCard) {
        // Garante conversão correta do ID do cartão
        const creditCardId =
          typeof selectedSavedCard.id === "string"
            ? parseInt(selectedSavedCard.id)
            : selectedSavedCard.id;

        console.log("💳 [PAYMENT] Dados do cartão para pagamento:", {
          originalId: selectedSavedCard.id,
          convertedId: creditCardId,
          brand: selectedSavedCard.brand,
          lastFour:
            selectedSavedCard.lastFourDigits ||
            selectedSavedCard.number?.slice(-4)
        });

        paymentData.creditCard = {
          creditCardId: creditCardId,
          installmentCount: 1 // Por padrão, pode ser configurável
        };
      } else if (method === PaymentType.Boleto) {
        paymentData.boleto = {
          installmentCount: 1 // Por padrão, pode ser configurável
        };
      }
      // PIX e Free não precisam de dados adicionais

      console.log("📤 [PAYMENT] Enviando dados para API:", {
        ...paymentData,
        creditCard: paymentData.creditCard
          ? {
              creditCardId: paymentData.creditCard.creditCardId,
              installmentCount: paymentData.creditCard.installmentCount
            }
          : undefined
      });

      const payment = await createPaymentMutation.mutateAsync(paymentData);

      console.log(
        "✅ [PAYMENT] Pagamento criado com sucesso:",
        payment.data.id
      );

      // Redireciona para tela de confirmação com dados completos do pagamento
      router.push({
        pathname: "/(logged-stack)/payment-confirmation",
        params: {
          paymentId: payment.data.id,
          paymentData: JSON.stringify(payment.data), // Passa dados completos
          skipApiCall: "true" // Flag para não fazer chamada da API
        }
      });
    } catch (error) {
      console.error("❌ [PAYMENT] Erro ao processar pagamento:", error);

      // Tratamento de erro mais específico
      let errorMessage = t("payment.error.message");

      if (error && typeof error === "object" && "response" in error) {
        const apiError = error as any;
        console.error("❌ [PAYMENT] Detalhes do erro da API:", {
          status: apiError.response?.status,
          data: apiError.response?.data,
          message: apiError.message
        });

        // Personalizar mensagem baseada no status
        if (apiError.response?.status === 400) {
          errorMessage = t("payment.error.invalidData");
        } else if (apiError.response?.status === 401) {
          errorMessage = t("payment.error.unauthorized");
        } else if (apiError.response?.status >= 500) {
          errorMessage = t("payment.error.serverError");
        }
      }

      Alert.alert(t("payment.error.title"), errorMessage, [
        {text: t("common.ok")}
      ]);
    }
  }, [
    selectedMethod,
    entity,
    eventId,
    amount,
    paymentItem.title,
    selectedSavedCard,
    createPaymentMutation,
    router,
    t
  ]);

  // Renderiza conteúdo baseado no passo atual
  const renderStepContent = () => {
    switch (step) {
      case "method":
        return (
          <PaymentMethodSelector
            selectedMethod={selectedMethod}
            onMethodSelect={handleMethodSelect}
            savedCreditCards={creditCards}
            onSelectSavedCard={handleSavedCardSelect}
            selectedSavedCard={selectedSavedCard}
            onAddNewCard={handleAddNewCard}
            showNewCardForm={showNewCardForm}
          />
        );

      case "details":
        if (selectedMethod === PaymentType.CreditCard) {
          return (
            <CreditCardForm
              onSubmit={handleCreditCardSubmit}
              onBack={handleBackToMethodSelection}
              isLoading={
                createCreditCardMutation.isPending ||
                createPaymentMutation.isPending
              }
            />
          );
        }
        return null;

      case "summary":
        return <PaymentSummary items={[paymentItem]} total={amount} />;

      default:
        return null;
    }
  };

  // Renderiza botões de ação
  const renderActionButtons = () => {
    console.log("🔍 [PAYMENT] Renderizando botões de ação:", {
      step,
      selectedMethod,
      selectedSavedCard: !!selectedSavedCard,
      canProceedToDetails,
      canProceedToSummary
    });

    if (step === "summary") {
      const isProcessingPayment = createPaymentMutation.isPending;

      return (
        <View style={styles.actionButtons}>
          <Button
            text="common.back"
            onPress={isProcessingPayment ? undefined : handlePreviousStep}
            style={[styles.actionButton, styles.secondaryButton]}
            backgroundColor="transparent"
            borderColor={styles.secondaryButtonBorder.borderColor}
            disabledColor={isProcessingPayment}
          />
          <Button
            text="payment.confirmPayment"
            onPress={isProcessingPayment ? undefined : handlePayment}
            style={[styles.actionButton, styles.primaryButton]}
            disabledColor={isProcessingPayment}
          />
        </View>
      );
    }

    if (step === "details") {
      return (
        <View style={styles.actionButtons}>
          <Button
            text="common.back"
            onPress={handlePreviousStep}
            style={[styles.actionButton, styles.secondaryButton]}
            backgroundColor="transparent"
            borderColor={styles.secondaryButtonBorder.borderColor}
          />
        </View>
      );
    }

    // Botão "Continuar" para step "method"
    if (step === "method") {
      // Verifica se pode prosseguir baseado no método selecionado
      // CORREÇÃO: selectedMethod pode ser 0 (PaymentType.CreditCard), então usar !== undefined
      const canProceed =
        selectedMethod !== undefined &&
        (selectedMethod !== PaymentType.CreditCard || // Métodos não-cartão sempre podem prosseguir
          selectedSavedCard || // Cartão salvo selecionado
          showNewCardForm || // Quer adicionar novo cartão
          creditCards.length === 0); // Não tem cartões salvos

      console.log("🔍 [PAYMENT] 🔧 DEBUG BOTÃO - Verificando condições:", {
        step,
        selectedMethod,
        selectedMethodName:
          selectedMethod === PaymentType.CreditCard
            ? "CreditCard"
            : selectedMethod === PaymentType.Pix
            ? "Pix"
            : selectedMethod === PaymentType.Boleto
            ? "Boleto"
            : "None",
        selectedSavedCard: !!selectedSavedCard,
        selectedSavedCardId: selectedSavedCard?.id,
        showNewCardForm,
        creditCardsLength: creditCards.length,
        canProceed,
        condition1: !!selectedMethod,
        condition2: selectedMethod !== PaymentType.CreditCard,
        condition3: !!selectedSavedCard,
        condition4: showNewCardForm,
        condition5: creditCards.length === 0
      });

      if (canProceed) {
        console.log("✅ [PAYMENT] 🔧 DEBUG BOTÃO - Mostrando botão Continuar");
        return (
          <View style={styles.actionButtons}>
            <Button
              text="common.next"
              onPress={handleNextStep}
              style={[styles.actionButton, styles.primaryButton]}
            />
          </View>
        );
      } else {
        console.log("❌ [PAYMENT] 🔧 DEBUG BOTÃO - Ocultando botão Continuar");
      }
    }

    return null;
  };

  // Mostrar loading enquanto carrega o evento
  if (isLoadingEvent) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingOverlay message={t("common.loading", "Carregando evento...")} />
      </SafeAreaView>
    );
  }

  // Mostrar erro se não conseguir carregar o evento
  if (eventError || !event) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {t("payment.error.eventNotFound", "Evento não encontrado")}
          </Text>
          <Button
            text="common.back"
            onPress={() => router.back()}
            style={styles.errorButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderStepContent()}
      </ScrollView>

      {renderActionButtons()}

      {(createPaymentMutation.isPending ||
        createCreditCardMutation.isPending) && <LoadingOverlay />}
    </SafeAreaView>
  );
};

export default PaymentScreen;
