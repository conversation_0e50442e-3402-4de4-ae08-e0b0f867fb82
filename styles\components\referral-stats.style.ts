import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16
  },

  // Compact styles
  compactContainer: {
    backgroundColor: "transparent",
    padding: 12,
    marginBottom: 8
  },
  compactRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8
  },
  compactStatItem: {
    flex: 1,
    alignItems: "center",
    paddingHorizontal: 8
  },
  compactStatValue: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: 700,
    lineHeight: 28,
    marginBottom: 2
  },
  compactStatLabel: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: 400,
    lineHeight: 14,
    textAlign: "center"
  },

  // Title styles
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 600,
    lineHeight: 26,
    marginBottom: 20,
    textAlign: "center"
  },

  // Stats grid
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: 24
  },
  statCard: {
    width: "48%",
    backgroundColor: stylesConstants.colors.primaryBackground,
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    marginBottom: 12
  },
  statIcon: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginBottom: 8
  },
  statValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32,
    marginBottom: 4
  },
  statLabel: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16,
    textAlign: "center"
  },

  // Monthly comparison
  monthlyContainer: {
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    paddingTop: 20
  },
  monthlyTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    marginBottom: 16,
    textAlign: "center"
  },
  monthlyRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
  },
  monthlyItem: {
    flex: 1,
    alignItems: "center"
  },
  monthlyValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: 700,
    lineHeight: 28,
    marginBottom: 4
  },
  monthlyLabel: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16,
    textAlign: "center"
  },
  monthlyDivider: {
    width: 1,
    height: 40,
    backgroundColor: stylesConstants.colors.borderDefault,
    marginHorizontal: 16
  },

  // Trend styles
  trendContainer: {
    alignItems: "center",
    marginTop: 12
  },
  trendText: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  trendPositive: {
    color: stylesConstants.colors.green400
  },
  trendNegative: {
    color: stylesConstants.colors.error500
  },

  // Loading and error states
  loadingText: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    textAlign: "center",
    marginTop: 8
  },
  errorText: {
    color: stylesConstants.colors.error500,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    textAlign: "center"
  }
});

export default styles;
