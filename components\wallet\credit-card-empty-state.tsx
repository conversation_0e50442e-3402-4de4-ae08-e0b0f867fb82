import React from "react";
import {View, Text, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import CreditCardIcon from "@/components/icons/credit-card-icon";
import styles from "@/styles/components/wallet/credit-card-empty-state.style";

export interface CreditCardEmptyStateProps {
  onAddCard?: () => void;
}

const CreditCardEmptyState: React.FC<CreditCardEmptyStateProps> = ({
  onAddCard
}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <CreditCardIcon width={48} height={48} />
      </View>
      <Text style={styles.title}>
        {t("creditCardEmptyState.title", "Você ainda não adicionou um cartão")}
      </Text>
      <Text style={styles.description}>
        {t(
          "creditCardEmptyState.description",
          "Com um cartão cadastrado, seus pagamentos ficam mais fáceis e rápidos. Que tal adicionar agora?"
        )}
      </Text>
      <TouchableOpacity
        style={styles.button}
        onPress={onAddCard}
        activeOpacity={0.8}
      >
        <Text style={styles.buttonText}>
          {t("creditCardEmptyState.buttonText", "Adicionar meu cartão")}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default CreditCardEmptyState;
