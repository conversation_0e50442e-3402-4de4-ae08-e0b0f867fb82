import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 20,
    marginVertical: 10
  },

  title: {
    fontSize: 18,
    fontWeight: "600",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 20,
    textAlign: "center",
    fontFamily: stylesConstants.fonts.inter
  },

  qrCodeContainer: {
    alignItems: "center",
    marginVertical: 20,
    padding: 20,
    backgroundColor: stylesConstants.colors.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },

  qrCodeImage: {
    width: 200,
    height: 200,
    borderRadius: 8
  },

  qrCodeLabel: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    marginTop: 12,
    textAlign: "center",
    fontFamily: stylesConstants.fonts.inter
  },

  codeContainer: {
    backgroundColor: stylesConstants.colors.gray50,
    borderRadius: 8,
    padding: 16,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },

  codeLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 8,
    fontFamily: stylesConstants.fonts.inter
  },

  codeText: {
    fontSize: 16,
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.inter,
    lineHeight: 24,
    textAlign: "center"
  },

  copyButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: stylesConstants.colors.brand.brand500,
    borderRadius: 8,
    padding: 12,
    marginTop: 12
  },

  copyButtonText: {
    color: stylesConstants.colors.white,
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 8,
    fontFamily: stylesConstants.fonts.inter
  },

  instructionsContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: stylesConstants.colors.gray50,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: stylesConstants.colors.brand.brand500
  },

  instructionsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 8,
    fontFamily: stylesConstants.fonts.inter
  },

  instructionItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 8
  },

  instructionNumber: {
    fontSize: 14,
    fontWeight: "600",
    color: stylesConstants.colors.brand.brand500,
    marginRight: 8,
    minWidth: 20,
    fontFamily: stylesConstants.fonts.inter
  },

  instructionText: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    flex: 1,
    lineHeight: 20,
    fontFamily: stylesConstants.fonts.inter
  },

  expirationContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: stylesConstants.colors.gray50,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.yellow400
  },

  expirationText: {
    fontSize: 14,
    color: stylesConstants.colors.yellow400,
    textAlign: "center",
    fontWeight: "500",
    fontFamily: stylesConstants.fonts.inter
  },

  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40
  },

  errorContainer: {
    alignItems: "center",
    padding: 20
  },

  errorText: {
    fontSize: 16,
    color: stylesConstants.colors.error,
    textAlign: "center",
    marginBottom: 16,
    fontFamily: stylesConstants.fonts.inter
  },

  retryButton: {
    backgroundColor: stylesConstants.colors.error,
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 12
  },

  retryButtonText: {
    color: stylesConstants.colors.white,
    fontSize: 14,
    fontWeight: "500",
    fontFamily: stylesConstants.fonts.inter
  },

  // Additional styles needed by the component
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16
  },

  description: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    marginBottom: 16,
    fontFamily: stylesConstants.fonts.inter
  },

  codeTextContainer: {
    backgroundColor: stylesConstants.colors.gray50,
    borderRadius: 8,
    padding: 16,
    marginVertical: 8
  },

  codeTextBox: {
    backgroundColor: stylesConstants.colors.gray50,
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },

  copiedCodeBox: {
    backgroundColor: stylesConstants.colors.gray50,
    borderColor: stylesConstants.colors.green400
  },

  copyIcon: {
    marginRight: 8
  },

  instructions: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    marginTop: 16,
    fontFamily: stylesConstants.fonts.inter
  },

  warningContainer: {
    backgroundColor: stylesConstants.colors.gray50,
    borderRadius: 8,
    padding: 12,
    marginTop: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.yellow400
  },

  warningText: {
    fontSize: 14,
    color: stylesConstants.colors.yellow400,
    textAlign: "center",
    fontFamily: stylesConstants.fonts.inter
  },

  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20
  },

  closeButton: {
    backgroundColor: stylesConstants.colors.gray200,
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 12
  }
});

export default styles;
