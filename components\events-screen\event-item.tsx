import React, {useMemo} from "react";
import {Text, TouchableOpacity, View} from "react-native";
import styles from "../../styles/components/events-screen/event-item.style";
import Badge, {BadgeColor} from "../badge";
import {useTranslation} from "react-i18next";
import stylesConstants from "../../styles/styles-constants";
import HandshakeIcon from "../icons/handshake-icon";
import CartPlusIcon from "../cart-plus-icon";
import AnnounceIcon from "../icons/announce-icon";
import ChevronRightIcon from "../icons/chevron-right-icon";
import {Event} from "../../models/api/events.models";

export interface EventItemProps {
  event?: Event;
  onPress?: (event: Event) => void;
  onAddToCart?: (event: Event) => void; // Now triggers direct purchase instead of adding to cart
  onPayNow?: (event: Event) => void;
}

const EventItem: React.FC<EventItemProps> = ({
  event,
  onPress,
  onAddToCart,
  onPayNow
}) => {
  const {t} = useTranslation();

  // Dados padrão se não houver evento
  const defaultEvent = {
    id: "default",
    title: "Encontro anual de empreendedores em Balneário Camboriú - SC",
    description:
      "Lorem ipsum quisque lobortis in eu rhoncus dui nulla lectus sagittis dictum dignissim...",
    category: {
      id: "business",
      name: "Negócios",
      slug: "business",
      color: "#34C759"
    },
    price: {
      amount: 7500, // R$ 75.00 in cents - different from default fallback
      currency: "BRL",
      isFree: false
    },
    value: 75 // Alternative price field for testing
  };

  const eventData = event || defaultEvent;

  // Log para debug dos dados do evento e preços
  if (event) {
    console.log("📅 Dados do evento recebido:", {
      id: event.id,
      title: event.title,
      category: event.category?.name,
      price: event.price,
      value: (event as any).value, // Campo alternativo de preço
      hasDescription: !!event.description,
      hasShortDescription: !!event.shortDescription
    });
  }

  const businessBadgeColor: BadgeColor = useMemo(
    () => ({
      borderColor: stylesConstants.colors.gray200,
      textColor: stylesConstants.colors.gray700,
      backgroundColor: stylesConstants.colors.gray50
    }),
    []
  );

  // Função para calcular preço total do evento - extrai preço individual de cada evento
  const calculateTotalCost = () => {
    // Verifica se o evento é gratuito
    if (eventData.price?.isFree) {
      return "Gratuito";
    }

    // Verifica múltiplos formatos de preço da API
    let priceValue = null;

    // Primeiro tenta o campo price.amount que agora deve estar mapeado corretamente
    if (eventData.price?.amount !== undefined) {
      priceValue = eventData.price.amount;
    } else if ((eventData as any).value !== undefined) {
      // Fallback para o campo 'value' direto
      priceValue = (eventData as any).value;
    } else if (typeof eventData.price === "number") {
      // Formato direto: price como número
      priceValue = eventData.price;
    }

    if (priceValue !== null && priceValue >= 0) {
      // Se o valor é 0, é gratuito
      if (priceValue === 0) {
        return "Gratuito";
      }

      // Converte centavos para reais (API retorna em centavos)
      const finalPrice = priceValue / 100;
      return finalPrice.toLocaleString("pt-BR", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    }

    // Valor padrão quando não há preço definido
    return "Consulte";
  };

  // Função para lidar com clique no evento
  const handleEventPress = () => {
    if (event && onPress) {
      onPress(event);
    }
  };

  // Função para lidar com compra direta (anteriormente adicionar ao carrinho)
  const handleAddToCart = () => {
    if (event && onAddToCart) {
      onAddToCart(event);
    }
  };

  // Função para lidar com pagamento direto
  const handlePayNow = () => {
    console.log("💳 [EVENT-ITEM] Botão 'Pagar Agora' clicado");
    console.log("📋 [EVENT-ITEM] Dados do evento:", {
      id: event?.id,
      title: event?.title,
      price: event?.price,
      value: (event as any)?.value
    });

    if (event && onPayNow) {
      console.log("✅ [EVENT-ITEM] Chamando handler onPayNow");
      onPayNow(event);
    } else {
      console.log("❌ [EVENT-ITEM] Evento ou handler não disponível");
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <View style={styles.badgeContainer}>
          <Badge
            icon={<HandshakeIcon stroke={stylesConstants.colors.gray700} />}
            color={businessBadgeColor}
            text={eventData.category?.name || t("eventList.badges.business")}
          />
        </View>
        <TouchableOpacity onPress={handleAddToCart}>
          <CartPlusIcon />
        </TouchableOpacity>
      </View>
      <View style={styles.titleContainer}>
        <View style={styles.iconContainer}>
          <AnnounceIcon
            replaceColor={stylesConstants.colors.fullWhite}
            width={24}
            height={24}
          />
        </View>
        <Text
          style={[styles.text, styles.titleText]}
          ellipsizeMode="tail"
          numberOfLines={2}
        >
          {eventData.title}
        </Text>
      </View>
      <Text style={[styles.text, styles.description]}>
        {(eventData as any).shortDescription ||
          eventData.description ||
          "Descrição não disponível"}
      </Text>
      <View style={styles.footerContainer}>
        <View>
          <Text style={styles.text}>{t("eventList.priceLabel", "Preço")}</Text>
          <Text style={[styles.text, styles.priceText]}>
            {calculateTotalCost() === "Gratuito" ||
            calculateTotalCost() === "Consulte"
              ? calculateTotalCost()
              : `R$ ${calculateTotalCost()}`}
          </Text>
        </View>
        <View style={styles.actionButtons}>
          {/* Botão de pagamento direto - só aparece se não for gratuito */}
          {calculateTotalCost() !== "Gratuito" &&
            calculateTotalCost() !== "Consulte" &&
            onPayNow && (
              <TouchableOpacity
                style={styles.payNowButton}
                onPress={handlePayNow}
              >
                <Text style={[styles.text, styles.payNowButtonText]}>
                  {t("eventList.payNow", "Pagar Agora")}
                </Text>
              </TouchableOpacity>
            )}
          <TouchableOpacity
            style={styles.viewEventButton}
            onPress={handleEventPress}
          >
            <Text style={[styles.text, styles.viewEventButtonText]}>
              {t("eventList.viewEvent")}
            </Text>
            <ChevronRightIcon />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default EventItem;
