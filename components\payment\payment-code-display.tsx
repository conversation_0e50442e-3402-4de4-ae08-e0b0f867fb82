/**
 * Componente para exibir códigos QR e códigos de barras de pagamentos
 * Usado para PIX e Boleto
 */

import React, {useCallback, useState} from "react";
import {View, Text, TouchableOpacity, Alert, Image} from "react-native";
import {useTranslation} from "react-i18next";
import * as Clipboard from "expo-clipboard";
import Button from "../button";
import LoadingOverlay from "../loading-overlay";
import {
  usePaymentQRCode,
  usePaymentBarCode
} from "../../hooks/api/use-payments";
import {PaymentType} from "../../models/api/payments.models";
import styles from "../../styles/components/payment/payment-code-display.style";
import CopyIcon from "../icons/copy-icon";
import PixIcon from "../icons/pix-icon";
import BoletoIcon from "../icons/boleto-icon";

export interface PaymentCodeDisplayProps {
  paymentId: string;
  paymentType: PaymentType;
  onClose?: () => void;
}

const PaymentCodeDisplay: React.FC<PaymentCodeDisplayProps> = ({
  paymentId,
  paymentType,
  onClose
}) => {
  const {t} = useTranslation();
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  // Hooks para buscar códigos
  const {
    data: qrCode,
    isLoading: isLoadingQR,
    error: qrError
  } = usePaymentQRCode(paymentType === PaymentType.Pix ? paymentId : "");

  const {
    data: barCode,
    isLoading: isLoadingBarCode,
    error: barCodeError
  } = usePaymentBarCode(paymentType === PaymentType.Boleto ? paymentId : "");

  // Handler para copiar código
  const handleCopyCode = useCallback(
    async (code: string, type: "qr" | "bar") => {
      try {
        await Clipboard.setStringAsync(code);
        setCopiedCode(code);
        
        const message = type === "qr" 
          ? t("payment.qrCode.copied") 
          : t("payment.barCode.copied");
          
        Alert.alert(
          t("common.success"),
          message,
          [{text: t("common.ok")}]
        );

        // Limpa o estado de copiado após 3 segundos
        setTimeout(() => setCopiedCode(null), 3000);
      } catch (error) {
        console.error("❌ [PAYMENT-CODE] Erro ao copiar código:", error);
        Alert.alert(
          t("common.error"),
          t("payment.code.copyError"),
          [{text: t("common.ok")}]
        );
      }
    },
    [t]
  );

  // Renderiza conteúdo para PIX
  const renderPixContent = () => {
    if (isLoadingQR) {
      return <LoadingOverlay message={t("payment.qrCode.loading")} />;
    }

    if (qrError || !qrCode) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {t("payment.qrCode.error")}
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.codeContainer}>
        <View style={styles.header}>
          <PixIcon />
          <Text style={styles.title}>{t("payment.qrCode.title")}</Text>
        </View>

        <Text style={styles.description}>
          {t("payment.qrCode.description")}
        </Text>

        {/* QR Code Image */}
        <View style={styles.qrCodeContainer}>
          <Image
            source={{uri: `data:image/png;base64,${qrCode}`}}
            style={styles.qrCodeImage}
            resizeMode="contain"
          />
        </View>

        {/* Código para copiar */}
        <View style={styles.codeTextContainer}>
          <Text style={styles.codeLabel}>
            {t("payment.qrCode.codeLabel")}
          </Text>
          <TouchableOpacity
            style={[
              styles.codeTextBox,
              copiedCode === qrCode && styles.copiedCodeBox
            ]}
            onPress={() => handleCopyCode(qrCode, "qr")}
          >
            <Text style={styles.codeText} numberOfLines={3}>
              {qrCode}
            </Text>
            <View style={styles.copyIcon}>
              <CopyIcon />
            </View>
          </TouchableOpacity>
        </View>

        <Text style={styles.instructions}>
          {t("payment.qrCode.instructions")}
        </Text>
      </View>
    );
  };

  // Renderiza conteúdo para Boleto
  const renderBoletoContent = () => {
    if (isLoadingBarCode) {
      return <LoadingOverlay message={t("payment.barCode.loading")} />;
    }

    if (barCodeError || !barCode) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {t("payment.barCode.error")}
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.codeContainer}>
        <View style={styles.header}>
          <BoletoIcon />
          <Text style={styles.title}>{t("payment.barCode.title")}</Text>
        </View>

        <Text style={styles.description}>
          {t("payment.barCode.description")}
        </Text>

        {/* Código de barras para copiar */}
        <View style={styles.codeTextContainer}>
          <Text style={styles.codeLabel}>
            {t("payment.barCode.codeLabel")}
          </Text>
          <TouchableOpacity
            style={[
              styles.codeTextBox,
              copiedCode === barCode && styles.copiedCodeBox
            ]}
            onPress={() => handleCopyCode(barCode, "bar")}
          >
            <Text style={styles.codeText} numberOfLines={2}>
              {barCode}
            </Text>
            <View style={styles.copyIcon}>
              <CopyIcon />
            </View>
          </TouchableOpacity>
        </View>

        <Text style={styles.instructions}>
          {t("payment.barCode.instructions")}
        </Text>

        <View style={styles.warningContainer}>
          <Text style={styles.warningText}>
            {t("payment.barCode.warning")}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {paymentType === PaymentType.Pix && renderPixContent()}
      {paymentType === PaymentType.Boleto && renderBoletoContent()}

      {onClose && (
        <View style={styles.actionButtons}>
          <Button
            text="common.close"
            onPress={onClose}
            style={styles.closeButton}
          />
        </View>
      )}
    </View>
  );
};

export default PaymentCodeDisplay;
