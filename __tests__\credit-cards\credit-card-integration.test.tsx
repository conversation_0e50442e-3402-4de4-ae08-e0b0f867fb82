/**
 * Credit Card Integration Tests
 * Tests the complete credit card functionality including API integration
 */

import { renderHook, waitFor } from '@testing-library/react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { useCreditCards, useCreateCreditCard } from '../../hooks/api/use-credit-cards';
import { CreditCardsService } from '../../services/api/credit-cards/credit-cards.service';
import { transformCreditCardToMemberCard } from '../../utils/credit-card-transform';
import { CreateCreditCardFormData, CreditCard } from '../../models/api/credit-cards.models';

// Mock the API service
jest.mock('../../services/api/credit-cards/credit-cards.service');

const mockCreditCardsService = CreditCardsService as jest.Mocked<typeof CreditCardsService>;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('Credit Card Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('useCreditCards Hook', () => {
    it('should fetch credit cards successfully', async () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            holderName: 'John Doe',
            lastFourDigits: '1234',
            expiryMonth: 12,
            expiryYear: 2025,
            brand: 'visa',
            userId: 1,
            createdAt: '2023-01-01T00:00:00Z',
            createdBy: 'user'
          }
        ],
        totalItems: 1,
        page: 1,
        pageSize: 10,
        pageCount: 1,
        hasPreviousPage: false,
        hasNextPage: false
      };

      mockCreditCardsService.getCreditCards.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useCreditCards(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockResponse);
      expect(mockCreditCardsService.getCreditCards).toHaveBeenCalledWith(undefined);
    });

    it('should handle API errors gracefully', async () => {
      const mockError = new Error('API Error');
      mockCreditCardsService.getCreditCards.mockRejectedValue(mockError);

      const { result } = renderHook(() => useCreditCards(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(mockError);
    });
  });

  describe('useCreateCreditCard Hook', () => {
    it('should create credit card successfully', async () => {
      const mockCardData: CreateCreditCardFormData = {
        holderName: 'Jane Doe',
        number: '****************',
        expiryMonth: 12,
        expiryYear: 2025,
        cvv: '123'
      };

      const mockCreatedCard: CreditCard = {
        id: 2,
        holderName: 'Jane Doe',
        lastFourDigits: '1111',
        expiryMonth: 12,
        expiryYear: 2025,
        brand: 'visa',
        userId: 1,
        createdAt: '2023-01-01T00:00:00Z',
        createdBy: 'user'
      };

      mockCreditCardsService.createCreditCard.mockResolvedValue(mockCreatedCard);

      const { result } = renderHook(() => useCreateCreditCard(), {
        wrapper: createWrapper(),
      });

      result.current.mutate(mockCardData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockCreatedCard);
      expect(mockCreditCardsService.createCreditCard).toHaveBeenCalledWith(mockCardData);
    });
  });

  describe('Data Transformation', () => {
    it('should transform credit card to member card format', () => {
      const creditCard: CreditCard = {
        id: 1,
        holderName: 'John Doe',
        lastFourDigits: '1234',
        expiryMonth: 12,
        expiryYear: 2025,
        brand: 'visa',
        userId: 1,
        createdAt: '2023-01-01T00:00:00Z',
        createdBy: 'user'
      };

      const memberCard = transformCreditCardToMemberCard(creditCard);

      expect(memberCard).toEqual({
        id: '1',
        name: 'John Doe',
        photo: expect.any(String),
        membershipType: 'Cartão de Crédito',
        cardNumber: '1234',
        federationUnit: 'ClubM',
        associationUnit: 'Cartão Registrado',
        activeSince: expect.any(String),
        isDefault: false,
        cardType: 'visa',
        expiryDate: '12/2025'
      });
    });
  });

  describe('Service Validation', () => {
    it('should validate credit card data correctly', () => {
      const validData: CreateCreditCardFormData = {
        holderName: 'John Doe',
        number: '****************',
        expiryMonth: 12,
        expiryYear: 2025,
        cvv: '123'
      };

      const errors = CreditCardsService.validateCreditCardData(validData);
      expect(errors).toHaveLength(0);
    });

    it('should detect validation errors', () => {
      const invalidData: CreateCreditCardFormData = {
        holderName: '',
        number: '123',
        expiryMonth: 13,
        expiryYear: 2020,
        cvv: '12'
      };

      const errors = CreditCardsService.validateCreditCardData(invalidData);
      expect(errors.length).toBeGreaterThan(0);
    });

    it('should format card numbers correctly', () => {
      const cardNumber = '****************';
      const formatted = CreditCardsService.formatCardNumber(cardNumber);
      expect(formatted).toBe('4111 1111 1111 1111');
    });

    it('should detect card brands correctly', () => {
      expect(CreditCardsService.detectCardBrand('****************')).toBe('visa');
      expect(CreditCardsService.detectCardBrand('****************')).toBe('mastercard');
      expect(CreditCardsService.detectCardBrand('***************')).toBe('american-express');
    });

    it('should validate card numbers using Luhn algorithm', () => {
      expect(CreditCardsService.validateCardNumber('****************')).toBe(true);
      expect(CreditCardsService.validateCardNumber('****************')).toBe(false);
    });
  });
});

describe('Error Scenarios', () => {
  it('should handle network errors', async () => {
    const networkError = new Error('Network Error');
    mockCreditCardsService.getCreditCards.mockRejectedValue(networkError);

    const { result } = renderHook(() => useCreditCards(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toEqual(networkError);
  });

  it('should handle validation errors during creation', async () => {
    const validationError = new Error('Validation Error');
    mockCreditCardsService.createCreditCard.mockRejectedValue(validationError);

    const { result } = renderHook(() => useCreateCreditCard(), {
      wrapper: createWrapper(),
    });

    const invalidData: CreateCreditCardFormData = {
      holderName: '',
      number: '123',
      expiryMonth: 13,
      expiryYear: 2020,
      cvv: '12'
    };

    result.current.mutate(invalidData);

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toEqual(validationError);
  });
});

describe('Edge Cases', () => {
  it('should handle empty credit card list', async () => {
    const emptyResponse = {
      data: [],
      totalItems: 0,
      page: 1,
      pageSize: 10,
      pageCount: 0,
      hasPreviousPage: false,
      hasNextPage: false
    };

    mockCreditCardsService.getCreditCards.mockResolvedValue(emptyResponse);

    const { result } = renderHook(() => useCreditCards(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data?.data).toHaveLength(0);
  });

  it('should handle pagination correctly', async () => {
    const paginatedResponse = {
      data: [/* mock data */],
      totalItems: 25,
      page: 2,
      pageSize: 10,
      pageCount: 3,
      hasPreviousPage: true,
      hasNextPage: true
    };

    mockCreditCardsService.getCreditCards.mockResolvedValue(paginatedResponse);

    const { result } = renderHook(() => useCreditCards({ page: 2, pageSize: 10 }), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(mockCreditCardsService.getCreditCards).toHaveBeenCalledWith({ page: 2, pageSize: 10 });
  });
});
