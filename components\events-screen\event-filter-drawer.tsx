import React, {useEffect, useRef} from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Animated,
  Dimensions,
  SafeAreaView
} from "react-native";
import styles from "../../styles/components/events-screen/event-filter-drawer.style";

export interface EventFilters {
  eventTypes: string[];
  isFree: boolean | null;
  dateRange?: {start: Date; end: Date};
}

export interface EventFilterDrawerProps {
  visible: boolean;
  filters: EventFilters;
  onFiltersChange: (filters: EventFilters) => void;
  onClose: () => void;
  onApply: () => void;
  onClear: () => void;
}

const EventFilterDrawer: React.FC<EventFilterDrawerProps> = ({
  visible,
  filters,
  onFiltersChange,
  onClose,
  onApply,
  onClear
}) => {
  const slideAnim = useRef(new Animated.Value(0)).current;
  const screenHeight = Dimensions.get("window").height;

  const eventTypeOptions = [
    {key: "patrocinado", label: "Patrocinado"},
    {key: "privado", label: "Privado"},
    {key: "vip", label: "VIP"},
    {key: "online", label: "Online"},
    {key: "oficial", label: "Oficial"}
  ];

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true
      }).start();
    }
  }, [visible, slideAnim]);

  const handleEventTypeToggle = (eventType: string) => {
    const newEventTypes = filters.eventTypes.includes(eventType)
      ? filters.eventTypes.filter((type) => type !== eventType)
      : [...filters.eventTypes, eventType];

    onFiltersChange({
      ...filters,
      eventTypes: newEventTypes
    });
  };

  const handleFreeToggle = () => {
    onFiltersChange({
      ...filters,
      isFree: filters.isFree ? null : true
    });
  };

  const handleBackdropPress = () => {
    onClose();
  };

  const translateY = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [screenHeight, 0]
  });

  const backdropOpacity = slideAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.5]
  });

  const isEventTypeSelected = (eventType: string) => {
    return filters.eventTypes.includes(eventType);
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <Animated.View style={[styles.backdrop, {opacity: backdropOpacity}]}>
          <TouchableOpacity
            style={styles.backdropTouchable}
            activeOpacity={1}
            onPress={handleBackdropPress}
          />
        </Animated.View>

        <Animated.View
          style={[
            styles.drawer,
            {
              transform: [{translateY}]
            }
          ]}
        >
          <SafeAreaView style={styles.safeArea}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.handle} />
              <Text style={styles.title}>Filtros</Text>
            </View>

            <ScrollView
              style={styles.content}
              showsVerticalScrollIndicator={false}
            >
              {/* Tipo de evento */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Tipo de evento</Text>
                <View style={styles.eventTypesContainer}>
                  <View style={styles.eventTypesRow}>
                    {eventTypeOptions.slice(0, 4).map((option) => (
                      <TouchableOpacity
                        key={option.key}
                        style={[
                          styles.eventTypeBadge,
                          isEventTypeSelected(option.key) &&
                            styles.eventTypeBadgeSelected
                        ]}
                        onPress={() => handleEventTypeToggle(option.key)}
                      >
                        <Text
                          style={[
                            styles.eventTypeBadgeText,
                            isEventTypeSelected(option.key) &&
                              styles.eventTypeBadgeTextSelected
                          ]}
                        >
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                  <View style={styles.eventTypesSecondRow}>
                    {eventTypeOptions.slice(4).map((option) => (
                      <TouchableOpacity
                        key={option.key}
                        style={[
                          styles.eventTypeBadge,
                          isEventTypeSelected(option.key) &&
                            styles.eventTypeBadgeSelected
                        ]}
                        onPress={() => handleEventTypeToggle(option.key)}
                      >
                        <Text
                          style={[
                            styles.eventTypeBadgeText,
                            isEventTypeSelected(option.key) &&
                              styles.eventTypeBadgeTextSelected
                          ]}
                        >
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                    {/* Badge Gratuito separado */}
                    <TouchableOpacity
                      style={[
                        styles.eventTypeBadge,
                        filters.isFree && styles.eventTypeBadgeSelected
                      ]}
                      onPress={handleFreeToggle}
                    >
                      <Text
                        style={[
                          styles.eventTypeBadgeText,
                          filters.isFree && styles.eventTypeBadgeTextSelected
                        ]}
                      >
                        Gratuito
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              {/* Data / horários - Placeholder por enquanto */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Data / horários</Text>
                <TouchableOpacity style={styles.dateSelector}>
                  <Text style={styles.dateSelectorText}>
                    Selecionar data e horário
                  </Text>
                </TouchableOpacity>
              </View>
            </ScrollView>

            {/* Buttons */}
            <View style={styles.buttonsContainer}>
              <TouchableOpacity style={styles.applyButton} onPress={onApply}>
                <Text style={styles.applyButtonText}>Aplicar filtros</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.clearButton} onPress={onClear}>
                <Text style={styles.clearButtonText}>Limpar filtros</Text>
              </TouchableOpacity>
            </View>
          </SafeAreaView>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default EventFilterDrawer;
