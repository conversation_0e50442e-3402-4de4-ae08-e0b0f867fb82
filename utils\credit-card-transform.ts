/**
 * Credit Card Data Transformation Utilities
 * Transforms API credit card data to match existing UI component expectations
 */

import { 
  CreditCard, 
  MemberCard, 
  CreditCardBrand, 
  CreditCardTransformOptions 
} from '../models/api/credit-cards.models';

/**
 * Transforms API CreditCard to UI MemberCard format
 */
export function transformCreditCardToMemberCard(
  creditCard: CreditCard,
  options: CreditCardTransformOptions = {}
): MemberCard {
  const {
    defaultPhoto = "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    defaultMembershipType = "Cartão de Crédito",
    defaultFederationUnit = "ClubM",
    defaultAssociationUnit = "Cartão Registrado"
  } = options;

  // Determine card type based on brand
  const cardType = getCardTypeFromBrand(creditCard.brand);
  
  // Format expiry date
  const expiryDate = formatExpiryDate(creditCard.expiryMonth, creditCard.expiryYear);
  
  // Format active since date
  const activeSince = formatActiveSince(creditCard.createdAt);

  return {
    id: creditCard.id.toString(),
    name: creditCard.holderName,
    photo: defaultPhoto,
    membershipType: defaultMembershipType,
    cardNumber: creditCard.lastFourDigits,
    federationUnit: defaultFederationUnit,
    associationUnit: defaultAssociationUnit,
    activeSince,
    isDefault: false, // API doesn't provide this info, could be enhanced later
    cardType,
    expiryDate
  };
}

/**
 * Transforms array of API CreditCards to UI MemberCards
 */
export function transformCreditCardsToMemberCards(
  creditCards: CreditCard[],
  options: CreditCardTransformOptions = {}
): MemberCard[] {
  return creditCards.map(card => transformCreditCardToMemberCard(card, options));
}

/**
 * Determines card type from brand string
 */
function getCardTypeFromBrand(brand: string): "member" | "american-express" | "visa" {
  const normalizedBrand = brand.toLowerCase();
  
  switch (normalizedBrand) {
    case 'visa':
      return 'visa';
    case 'american express':
    case 'amex':
      return 'american-express';
    default:
      return 'member'; // Default fallback
  }
}

/**
 * Formats expiry date from month/year to readable string
 */
function formatExpiryDate(month: number, year: number): string {
  const paddedMonth = month.toString().padStart(2, '0');
  return `${paddedMonth}/${year}`;
}

/**
 * Formats created date to "active since" format
 */
function formatActiveSince(createdAt: string): string {
  const date = new Date(createdAt);
  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ];
  
  const month = months[date.getMonth()];
  const year = date.getFullYear();
  
  return `${month} de ${year}`;
}

/**
 * Detects credit card brand from card number
 */
export function detectCreditCardBrand(cardNumber: string): CreditCardBrand {
  // Remove spaces and non-digits
  const cleanNumber = cardNumber.replace(/\D/g, '');
  
  // Visa: starts with 4
  if (/^4/.test(cleanNumber)) {
    return CreditCardBrand.VISA;
  }
  
  // Mastercard: starts with 5 or 2221-2720
  if (/^5[1-5]/.test(cleanNumber) || /^2(22[1-9]|2[3-9]|[3-6]|7[0-1]|720)/.test(cleanNumber)) {
    return CreditCardBrand.MASTERCARD;
  }
  
  // American Express: starts with 34 or 37
  if (/^3[47]/.test(cleanNumber)) {
    return CreditCardBrand.AMERICAN_EXPRESS;
  }
  
  // Discover: starts with 6
  if (/^6/.test(cleanNumber)) {
    return CreditCardBrand.DISCOVER;
  }
  
  // Diners: starts with 30, 36, 38
  if (/^3[068]/.test(cleanNumber)) {
    return CreditCardBrand.DINERS;
  }
  
  // JCB: starts with 35
  if (/^35/.test(cleanNumber)) {
    return CreditCardBrand.JCB;
  }
  
  return CreditCardBrand.UNKNOWN;
}

/**
 * Formats card number with spaces for display
 */
export function formatCardNumber(cardNumber: string): string {
  const cleanNumber = cardNumber.replace(/\D/g, '');
  return cleanNumber.replace(/(.{4})/g, '$1 ').trim();
}

/**
 * Masks card number showing only last 4 digits
 */
export function maskCardNumber(cardNumber: string): string {
  const cleanNumber = cardNumber.replace(/\D/g, '');
  if (cleanNumber.length < 4) return cardNumber;
  
  const lastFour = cleanNumber.slice(-4);
  const masked = '*'.repeat(cleanNumber.length - 4);
  return formatCardNumber(masked + lastFour);
}

/**
 * Validates card number using Luhn algorithm
 */
export function validateCardNumber(cardNumber: string): boolean {
  const cleanNumber = cardNumber.replace(/\D/g, '');
  
  if (cleanNumber.length < 13 || cleanNumber.length > 19) {
    return false;
  }
  
  let sum = 0;
  let isEven = false;
  
  // Loop through values starting from the right
  for (let i = cleanNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cleanNumber.charAt(i), 10);
    
    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    isEven = !isEven;
  }
  
  return sum % 10 === 0;
}

/**
 * Validates expiry date
 */
export function validateExpiryDate(month: number, year: number): boolean {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;
  
  if (month < 1 || month > 12) return false;
  if (year < currentYear) return false;
  if (year === currentYear && month < currentMonth) return false;
  
  return true;
}

/**
 * Validates CVV
 */
export function validateCVV(cvv: string, brand: CreditCardBrand): boolean {
  const cleanCVV = cvv.replace(/\D/g, '');
  
  if (brand === CreditCardBrand.AMERICAN_EXPRESS) {
    return cleanCVV.length === 4;
  }
  
  return cleanCVV.length === 3;
}
