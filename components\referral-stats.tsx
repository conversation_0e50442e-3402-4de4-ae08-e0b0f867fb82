/**
 * Indication Stats Component
 * Displays indication statistics and metrics
 */

import React from "react";
import {View, Text, ActivityIndicator} from "react-native";
import {useTranslation} from "react-i18next";
import {useIndicationStats} from "@/hooks/api/use-indications";
import styles from "@/styles/components/referral-stats.style";

interface ReferralStatsProps {
  compact?: boolean;
}

const ReferralStats: React.FC<ReferralStatsProps> = ({compact = false}) => {
  const {t} = useTranslation();
  const {data: stats, isLoading, error} = useIndicationStats();

  if (isLoading) {
    return (
      <View style={[styles.container, compact && styles.compactContainer]}>
        <ActivityIndicator size="small" color="#007AFF" />
        <Text style={styles.loadingText}>
          {t("indications.stats.loading", "Carregando estatísticas...")}
        </Text>
      </View>
    );
  }

  // If stats endpoint doesn't exist (404), show default stats
  if (error || !stats) {
    const defaultStats = {
      totalIndications: 0,
      pendingIndications: 0,
      acceptedIndications: 0,
      rejectedIndications: 0,
      cancelledIndications: 0,
      expiredIndications: 0,
      conversionRate: 0,
      thisMonthIndications: 0,
      lastMonthIndications: 0
    };

    return renderStats(defaultStats, compact, t);
  }

  return renderStats(stats, compact, t);
};

const renderStats = (stats: any, compact: boolean, t: any) => {
  const statsData = [
    {
      key: "total",
      label: t("indications.stats.total", "Total de Convites"),
      value: stats.totalIndications,
      color: "#007AFF"
    },
    {
      key: "pending",
      label: t("indications.stats.pending", "Pendentes"),
      value: stats.pendingIndications,
      color: "#FFA500"
    },
    {
      key: "accepted",
      label: t("indications.stats.accepted", "Aceitos"),
      value: stats.acceptedIndications,
      color: "#4CAF50"
    },
    {
      key: "conversion",
      label: t("indications.stats.conversion", "Taxa de Conversão"),
      value: `${Math.round(stats.conversionRate * 100)}%`,
      color: "#9C27B0"
    }
  ];

  if (compact) {
    return (
      <View style={styles.compactContainer}>
        <View style={styles.compactRow}>
          {statsData.slice(0, 2).map((stat) => (
            <View key={stat.key} style={styles.compactStatItem}>
              <Text style={[styles.compactStatValue, {color: stat.color}]}>
                {stat.value}
              </Text>
              <Text style={styles.compactStatLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>
        <View style={styles.compactRow}>
          {statsData.slice(2).map((stat) => (
            <View key={stat.key} style={styles.compactStatItem}>
              <Text style={[styles.compactStatValue, {color: stat.color}]}>
                {stat.value}
              </Text>
              <Text style={styles.compactStatLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        {t("indications.stats.title", "Estatísticas de Indicações")}
      </Text>

      <View style={styles.statsGrid}>
        {statsData.map((stat) => (
          <View key={stat.key} style={styles.statCard}>
            <View style={[styles.statIcon, {backgroundColor: stat.color}]} />
            <Text style={styles.statValue}>{stat.value}</Text>
            <Text style={styles.statLabel}>{stat.label}</Text>
          </View>
        ))}
      </View>

      {stats.thisMonthIndications !== undefined &&
        stats.lastMonthIndications !== undefined && (
          <View style={styles.monthlyContainer}>
            <Text style={styles.monthlyTitle}>
              {t("indications.stats.monthly", "Comparação Mensal")}
            </Text>

            <View style={styles.monthlyRow}>
              <View style={styles.monthlyItem}>
                <Text style={styles.monthlyValue}>
                  {stats.thisMonthIndications}
                </Text>
                <Text style={styles.monthlyLabel}>
                  {t("indications.stats.thisMonth", "Este Mês")}
                </Text>
              </View>

              <View style={styles.monthlyDivider} />

              <View style={styles.monthlyItem}>
                <Text style={styles.monthlyValue}>
                  {stats.lastMonthIndications}
                </Text>
                <Text style={styles.monthlyLabel}>
                  {t("indications.stats.lastMonth", "Mês Passado")}
                </Text>
              </View>
            </View>

            {stats.thisMonthIndications !== stats.lastMonthIndications && (
              <View style={styles.trendContainer}>
                <Text
                  style={[
                    styles.trendText,
                    stats.thisMonthIndications > stats.lastMonthIndications
                      ? styles.trendPositive
                      : styles.trendNegative
                  ]}
                >
                  {stats.thisMonthIndications > stats.lastMonthIndications
                    ? `+${
                        stats.thisMonthIndications - stats.lastMonthIndications
                      } ${t("indications.stats.increase", "aumento")}`
                    : `${
                        stats.thisMonthIndications - stats.lastMonthIndications
                      } ${t("indications.stats.decrease", "diminuição")}`}
                </Text>
              </View>
            )}
          </View>
        )}
    </View>
  );
};

export default ReferralStats;
