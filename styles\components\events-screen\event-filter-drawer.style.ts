import {StyleSheet, Dimensions} from "react-native";
import stylesConstants from "../../styles-constants";

const {height: screenHeight} = Dimensions.get("window");

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "flex-end"
  },
  backdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "black"
  },
  backdropTouchable: {
    flex: 1
  },
  drawer: {
    backgroundColor: "#111828",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    borderTopWidth: 1.5,
    borderLeftWidth: 1.5,
    borderRightWidth: 1.5,
    borderColor: "#282A2E",
    maxHeight: screenHeight * 0.8,
    minHeight: 452
  },
  safeArea: {
    flex: 1
  },
  header: {
    alignItems: "center",
    paddingTop: 8,
    paddingBottom: 16,
    gap: 24
  },
  handle: {
    width: 44,
    height: 4,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 2
  },
  title: {
    color: "#DFE9F0",
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center"
  },
  content: {
    flex: 1,
    paddingHorizontal: 24
  },
  section: {
    marginBottom: 20
  },
  sectionTitle: {
    color: "#DFE9F0",
    fontSize: 12,
    lineHeight: 18,
    marginBottom: 12
  },
  eventTypesContainer: {
    gap: 8
  },
  eventTypesRow: {
    flexDirection: "row",
    gap: 12
  },
  eventTypesSecondRow: {
    flexDirection: "row",
    gap: 12
  },
  eventTypeBadge: {
    borderWidth: 1,
    borderColor: "#FCFCFD",
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 2,
    alignItems: "center",
    justifyContent: "center"
  },
  eventTypeBadgeSelected: {
    backgroundColor: "#F9FAFB",
    borderColor: "#EAECF0"
  },
  eventTypeBadgeText: {
    color: "#FCFCFD",
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },
  eventTypeBadgeTextSelected: {
    color: "#1D2939"
  },
  dateSelector: {
    backgroundColor: "#1C2230",
    borderWidth: 1,
    borderColor: "#F2F4F7",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    alignItems: "center",
    justifyContent: "center"
  },
  dateSelectorText: {
    color: "#F2F4F7",
    fontSize: 14,
    lineHeight: 20
  },
  buttonsContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
    gap: 6
  },
  applyButton: {
    backgroundColor: "#0F7C4D",
    borderWidth: 1,
    borderColor: "#0F7C4D",
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: "center",
    justifyContent: "center",
    height: 48
  },
  applyButtonText: {
    color: "#FCFCFD",
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24
  },
  clearButton: {
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: "center",
    justifyContent: "center",
    height: 48
  },
  clearButtonText: {
    color: "#FCFCFD",
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  }
});

export default styles;
