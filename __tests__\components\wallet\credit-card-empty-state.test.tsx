import React from "react";
import {render, fireEvent} from "@testing-library/react-native";
import CreditCardEmptyState from "../../../components/wallet/credit-card-empty-state";

// Mock react-i18next
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue: string) => defaultValue
  })
}));

describe("CreditCardEmptyState", () => {
  it("renders correctly", () => {
    const {getByText} = render(<CreditCardEmptyState />);

    expect(getByText("Você ainda não adicionou um cartão")).toBeTruthy();
    expect(
      getByText(
        "Com um cartão cadastrado, seus pagamentos ficam mais fáceis e rápidos. Que tal adicionar agora?"
      )
    ).toBeTruthy();
    expect(getByText("Adicionar meu cartão")).toBeTruthy();
  });

  it("calls onAddCard when button is pressed", () => {
    const mockOnAddCard = jest.fn();
    const {getByText} = render(
      <CreditCardEmptyState onAddCard={mockOnAddCard} />
    );

    const button = getByText("Adicionar meu cartão");
    fireEvent.press(button);

    expect(mockOnAddCard).toHaveBeenCalledTimes(1);
  });

  it("renders without onAddCard prop", () => {
    const {getByText} = render(<CreditCardEmptyState />);

    const button = getByText("Adicionar meu cartão");
    fireEvent.press(button);

    // Should not crash when onAddCard is undefined
    expect(button).toBeTruthy();
  });
});
