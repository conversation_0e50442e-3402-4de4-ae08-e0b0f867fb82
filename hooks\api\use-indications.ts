import {useMutation, useQuery, useQueryClient} from "@tanstack/react-query";
import {Alert} from "react-native";
import {useTranslation} from "react-i18next";

import {
  CreateIndicationRequest,
  IndicationStats,
  IndicationsListParams
} from "@/models/api/indications.models";
import {IndicationsService} from "@/services/api/indications.service";

/**
 * Hook para listar indicações
 */
export function useIndications(params?: IndicationsListParams) {
  return useQuery({
    queryKey: ["indications", "list", params],
    queryFn: () => IndicationsService.getIndications(params),
    select: (response) => response.data,
    staleTime: 5 * 60 * 1000 // 5 minutos
  });
}

/**
 * Hook para buscar uma indicação específica
 */
export function useIndication(id: string) {
  return useQuery({
    queryKey: ["indications", "detail", id],
    queryFn: () => IndicationsService.getIndicationById(id),
    select: (response) => response.data,
    enabled: !!id,
    staleTime: 5 * 60 * 1000 // 5 minutos
  });
}

/**
 * Hook para criar uma nova indicação
 */
export function useCreateIndication() {
  const queryClient = useQueryClient();
  const {t} = useTranslation();

  return useMutation({
    mutationFn: (data: CreateIndicationRequest) =>
      IndicationsService.createIndication(data),
    onSuccess: (response) => {
      if (response.success) {
        // Invalida as queries de indicações para recarregar a lista
        queryClient.invalidateQueries({
          queryKey: ["indications"]
        });

        console.log(
          "✅ [INDICATIONS] Indicação criada com sucesso:",
          response.data
        );
      } else {
        console.error("❌ [INDICATIONS] Erro na resposta:", response.message);
        Alert.alert(
          t("indications.error.title", "Erro"),
          response.message ||
            t("indications.error.generic", "Erro ao criar indicação")
        );
      }
    },
    onError: (error: any) => {
      console.error("❌ [INDICATIONS] Erro ao criar indicação:", error);
      Alert.alert(
        t("indications.error.title", "Erro"),
        error.message ||
          t("indications.error.generic", "Erro ao criar indicação")
      );
    }
  });
}

/**
 * Hook para buscar estatísticas de indicações
 */
export function useIndicationStats() {
  return useQuery({
    queryKey: ["indications", "stats"],
    queryFn: () => IndicationsService.getIndicationStats(),
    select: (response) => response.data,
    staleTime: 5 * 60 * 1000 // 5 minutos
  });
}

/**
 * Hook para invalidar cache de indicações
 */
export function useInvalidateIndications() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({
      queryKey: ["indications"]
    });
  };
}
