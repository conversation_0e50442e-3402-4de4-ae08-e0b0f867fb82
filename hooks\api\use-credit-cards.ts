/**
 * React Query hooks for Credit Cards
 * Provides data fetching, caching, and mutation capabilities for credit card operations
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions,
  useInfiniteQuery,
  UseInfiniteQueryOptions
} from "@tanstack/react-query";
import {useCallback} from "react";
import {CreditCardsService} from "@/services/api/credit-cards/credit-cards.service";
import {
  CreditCard,
  CreateCreditCardRequest,
  CreditCardsResponse,
  CreditCardsListParams
} from "@/models/api/credit-cards.models";
import {BaseApiError} from "@/services/api/base/api-errors";
import {ApiLogger} from "@/services/api/base/api-logger";

/**
 * Query keys for credit cards cache management
 */
export const creditCardsKeys = {
  all: ["credit-cards"] as const,
  lists: () => [...creditCardsKeys.all, "list"] as const,
  list: (params?: CreditCardsListParams) =>
    [...creditCardsKeys.lists(), params] as const,
  details: () => [...creditCardsKeys.all, "detail"] as const,
  detail: (id: number) => [...creditCardsKeys.details(), id] as const,
  user: () => [...creditCardsKeys.all, "user"] as const,
  userList: (params?: CreditCardsListParams) =>
    [...creditCardsKeys.user(), params] as const
};

/**
 * Hook to fetch paginated list of credit cards
 */
export const useCreditCards = (
  params?: CreditCardsListParams,
  options?: UseQueryOptions<CreditCardsResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: creditCardsKeys.list(params),
    queryFn: () => CreditCardsService.getCreditCards(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry for authentication/authorization errors
      if (
        error instanceof BaseApiError &&
        (error.status === 401 || error.status === 403)
      ) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options
  });
};

/**
 * Hook to fetch infinite list of credit cards (for pagination)
 */
export const useInfiniteCreditCards = (
  params?: Omit<CreditCardsListParams, "page">,
  options?: UseInfiniteQueryOptions<CreditCardsResponse, BaseApiError>
) => {
  return useInfiniteQuery({
    queryKey: creditCardsKeys.list(params),
    queryFn: ({pageParam = 1}) =>
      CreditCardsService.getCreditCards({...params, page: pageParam as number}),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.hasNextPage ? lastPage.page + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.hasPreviousPage ? firstPage.page - 1 : undefined;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    ...options
  });
};

/**
 * Hook to fetch a specific credit card by ID
 */
export const useCreditCard = (
  id: number,
  options?: UseQueryOptions<CreditCard, BaseApiError>
) => {
  return useQuery({
    queryKey: creditCardsKeys.detail(id),
    queryFn: () => CreditCardsService.getCreditCard(id),
    enabled: !!id && id > 0,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: (failureCount, error) => {
      // Don't retry for 404 (card not found)
      if (error instanceof BaseApiError && error.status === 404) {
        return false;
      }
      // Don't retry for authentication/authorization errors
      if (
        error instanceof BaseApiError &&
        (error.status === 401 || error.status === 403)
      ) {
        return false;
      }
      // Retry for server errors
      return (
        failureCount < 3 && error instanceof BaseApiError && error.status >= 500
      );
    },
    ...options
  });
};

/**
 * Hook to create a new credit card
 */
export const useCreateCreditCard = (
  options?: UseMutationOptions<
    CreditCard,
    BaseApiError,
    CreateCreditCardRequest
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (creditCardData: CreateCreditCardRequest) =>
      CreditCardsService.createCreditCard(creditCardData),
    onSuccess: (newCard, variables) => {
      ApiLogger.info("Credit card created successfully", {
        id: newCard.id,
        holderName: newCard.holderName
      });

      // Invalidate and refetch credit cards list
      queryClient.invalidateQueries({
        queryKey: creditCardsKeys.lists()
      });

      // Add the new card to the cache
      queryClient.setQueryData(creditCardsKeys.detail(newCard.id), newCard);

      // Update lists cache if possible
      queryClient.setQueriesData(
        {queryKey: creditCardsKeys.lists()},
        (oldData: CreditCardsResponse | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            data: [newCard, ...oldData.data],
            totalItems: oldData.totalItems + 1
          };
        }
      );
    },
    onError: (error, variables) => {
      ApiLogger.error("Failed to create credit card", error as Error, {
        holderName: variables.holderName
      });
    },
    retry: (failureCount, error) => {
      // Don't retry mutations for most errors
      if (error instanceof BaseApiError) {
        // Only retry for network errors
        return failureCount < 1 && error.status >= 500;
      }
      return false;
    },
    ...options
  });
};

/**
 * Hook to invalidate credit cards cache
 */
export const useInvalidateCreditCards = () => {
  const queryClient = useQueryClient();

  return useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: creditCardsKeys.all
    });
  }, [queryClient]);
};

/**
 * Hook to prefetch credit cards
 */
export const usePrefetchCreditCards = () => {
  const queryClient = useQueryClient();

  return useCallback(
    (params?: CreditCardsListParams) => {
      queryClient.prefetchQuery({
        queryKey: creditCardsKeys.list(params),
        queryFn: () => CreditCardsService.getCreditCards(params),
        staleTime: 5 * 60 * 1000
      });
    },
    [queryClient]
  );
};

/**
 * Hook to prefetch a specific credit card
 */
export const usePrefetchCreditCard = () => {
  const queryClient = useQueryClient();

  return useCallback(
    (id: number) => {
      queryClient.prefetchQuery({
        queryKey: creditCardsKeys.detail(id),
        queryFn: () => CreditCardsService.getCreditCard(id),
        staleTime: 5 * 60 * 1000
      });
    },
    [queryClient]
  );
};

/**
 * Hook to get cached credit card data without triggering a request
 */
export const useCachedCreditCard = (id: number) => {
  const queryClient = useQueryClient();

  return useCallback(() => {
    return queryClient.getQueryData<CreditCard>(creditCardsKeys.detail(id));
  }, [queryClient, id]);
};

/**
 * Hook to manually update credit card cache
 */
export const useUpdateCreditCardCache = () => {
  const queryClient = useQueryClient();

  return useCallback(
    (
      id: number,
      updater: (oldData: CreditCard | undefined) => CreditCard | undefined
    ) => {
      queryClient.setQueryData(creditCardsKeys.detail(id), updater);
    },
    [queryClient]
  );
};

/**
 * Hook to remove credit card from cache
 */
export const useRemoveCreditCardFromCache = () => {
  const queryClient = useQueryClient();

  return useCallback(
    (id: number) => {
      // Remove from detail cache
      queryClient.removeQueries({
        queryKey: creditCardsKeys.detail(id)
      });

      // Update lists cache to remove the card
      queryClient.setQueriesData(
        {queryKey: creditCardsKeys.lists()},
        (oldData: CreditCardsResponse | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            data: oldData.data.filter((card) => card.id !== id),
            totalItems: Math.max(0, oldData.totalItems - 1)
          };
        }
      );
    },
    [queryClient]
  );
};

/**
 * Utility to clear all credit cards cache
 */
export const useClearCreditCardsCache = () => {
  const queryClient = useQueryClient();

  return useCallback(() => {
    queryClient.removeQueries({
      queryKey: creditCardsKeys.all
    });
  }, [queryClient]);
};
