/**
 * Credit Card Error Handler Hook
 * Specialized error handling for credit card operations
 */

import { useCallback } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { 
  BaseApiError, 
  ValidationError, 
  NetworkError, 
  AuthenticationError,
  ForbiddenError,
  ServerError,
  RateLimitError
} from '@/services/api/base/api-errors';
import { ApiLogger } from '@/services/api/base/api-logger';

export interface CreditCardErrorContext {
  operation: 'create' | 'read' | 'list' | 'update' | 'delete';
  cardData?: {
    holderName?: string;
    lastFourDigits?: string;
    brand?: string;
  };
}

export interface CreditCardErrorHandlerOptions {
  showAlert?: boolean;
  logError?: boolean;
  context?: CreditCardErrorContext;
  onRetry?: () => void;
  onCancel?: () => void;
}

export interface CreditCardErrorResult {
  handled: boolean;
  message: string;
  shouldRetry: boolean;
  shouldRedirect: boolean;
  userAction?: 'retry' | 'cancel' | 'login' | 'contact_support';
}

export const useCreditCardErrorHandler = () => {
  const { t } = useTranslation();
  const router = useRouter();

  /**
   * Get localized error message for credit card operations
   */
  const getCreditCardErrorMessage = useCallback((
    error: BaseApiError,
    context?: CreditCardErrorContext
  ): string => {
    const operation = context?.operation || 'unknown';

    // Specific credit card error codes
    switch (error.code) {
      case 'CARD_ALREADY_EXISTS':
        return t('creditCard.errors.cardAlreadyExists', 'Este cartão já está cadastrado em sua conta.');
      
      case 'INVALID_CARD_NUMBER':
        return t('creditCard.errors.invalidCardNumber', 'Número do cartão inválido. Verifique os dígitos.');
      
      case 'EXPIRED_CARD':
        return t('creditCard.errors.expiredCard', 'Este cartão está expirado.');
      
      case 'INVALID_CVV':
        return t('creditCard.errors.invalidCvv', 'Código CVV inválido.');
      
      case 'INVALID_EXPIRY_DATE':
        return t('creditCard.errors.invalidExpiryDate', 'Data de expiração inválida.');
      
      case 'UNSUPPORTED_CARD_BRAND':
        return t('creditCard.errors.unsupportedBrand', 'Bandeira do cartão não suportada.');
      
      case 'CARD_LIMIT_EXCEEDED':
        return t('creditCard.errors.limitExceeded', 'Limite de cartões cadastrados atingido.');
      
      case 'CARD_NOT_FOUND':
        return t('creditCard.errors.cardNotFound', 'Cartão não encontrado.');
      
      case 'CARD_BLOCKED':
        return t('creditCard.errors.cardBlocked', 'Este cartão está bloqueado.');
      
      default:
        // Generic messages based on operation
        switch (operation) {
          case 'create':
            return t('creditCard.errors.createFailed', 'Erro ao adicionar cartão. Tente novamente.');
          case 'read':
          case 'list':
            return t('creditCard.errors.loadFailed', 'Erro ao carregar cartões. Tente novamente.');
          case 'update':
            return t('creditCard.errors.updateFailed', 'Erro ao atualizar cartão. Tente novamente.');
          case 'delete':
            return t('creditCard.errors.deleteFailed', 'Erro ao remover cartão. Tente novamente.');
          default:
            return error.message || t('creditCard.errors.generic', 'Erro ao processar cartão.');
        }
    }
  }, [t]);

  /**
   * Handle credit card specific errors
   */
  const handleCreditCardError = useCallback((
    error: BaseApiError | Error,
    options: CreditCardErrorHandlerOptions = {}
  ): CreditCardErrorResult => {
    const {
      showAlert = true,
      logError = true,
      context,
      onRetry,
      onCancel
    } = options;

    let handled = false;
    let message = '';
    let shouldRetry = false;
    let shouldRedirect = false;
    let userAction: CreditCardErrorResult['userAction'] = undefined;

    if (logError) {
      ApiLogger.error('Credit Card Error', error, {
        context,
        timestamp: new Date().toISOString()
      });
    }

    if (error instanceof BaseApiError) {
      handled = true;
      message = getCreditCardErrorMessage(error, context);

      // Handle specific error types
      if (error instanceof ValidationError) {
        shouldRetry = true;
        userAction = 'retry';
      } else if (error instanceof NetworkError) {
        shouldRetry = true;
        userAction = 'retry';
        message = t('creditCard.errors.networkError', 'Erro de conexão. Verifique sua internet.');
      } else if (error instanceof AuthenticationError) {
        shouldRedirect = true;
        userAction = 'login';
        message = t('creditCard.errors.authError', 'Sessão expirada. Faça login novamente.');
      } else if (error instanceof ForbiddenError) {
        userAction = 'contact_support';
        message = t('creditCard.errors.forbidden', 'Acesso negado. Entre em contato com o suporte.');
      } else if (error instanceof ServerError) {
        shouldRetry = true;
        userAction = 'retry';
        message = t('creditCard.errors.serverError', 'Erro no servidor. Tente novamente em alguns minutos.');
      } else if (error instanceof RateLimitError) {
        userAction = 'retry';
        message = t('creditCard.errors.rateLimited', 'Muitas tentativas. Aguarde um momento e tente novamente.');
      }
    } else {
      // Generic error
      handled = true;
      message = error.message || t('creditCard.errors.unknown', 'Erro desconhecido.');
      shouldRetry = true;
      userAction = 'retry';
    }

    // Show alert if requested
    if (showAlert) {
      showCreditCardErrorAlert(message, userAction, onRetry, onCancel);
    }

    // Handle redirects
    if (shouldRedirect && userAction === 'login') {
      router.push('/auth/login');
    }

    return {
      handled,
      message,
      shouldRetry,
      shouldRedirect,
      userAction
    };
  }, [t, router, getCreditCardErrorMessage]);

  /**
   * Show credit card error alert with appropriate actions
   */
  const showCreditCardErrorAlert = useCallback((
    message: string,
    userAction?: CreditCardErrorResult['userAction'],
    onRetry?: () => void,
    onCancel?: () => void
  ) => {
    const buttons: any[] = [];

    // Add retry button if applicable
    if (userAction === 'retry' && onRetry) {
      buttons.push({
        text: t('common.tryAgain', 'Tentar Novamente'),
        onPress: onRetry
      });
    }

    // Add contact support button if needed
    if (userAction === 'contact_support') {
      buttons.push({
        text: t('common.contactSupport', 'Contatar Suporte'),
        onPress: () => {
          // Could open email or support chat
          console.log('Contact support requested');
        }
      });
    }

    // Add cancel/ok button
    buttons.push({
      text: userAction === 'login' ? t('common.login', 'Fazer Login') : t('common.ok', 'OK'),
      style: userAction === 'login' ? 'default' : 'cancel',
      onPress: userAction === 'login' ? () => router.push('/auth/login') : onCancel
    });

    Alert.alert(
      t('creditCard.errors.title', 'Erro no Cartão'),
      message,
      buttons
    );
  }, [t, router]);

  /**
   * Wrapper for credit card operations with error handling
   */
  const withCreditCardErrorHandling = useCallback(<T extends any[], R>(
    operation: CreditCardErrorContext['operation'],
    fn: (...args: T) => Promise<R>,
    options?: Omit<CreditCardErrorHandlerOptions, 'context'>
  ) => {
    return async (...args: T): Promise<R | null> => {
      try {
        return await fn(...args);
      } catch (error) {
        const result = handleCreditCardError(error as BaseApiError, {
          ...options,
          context: { operation }
        });
        
        if (result.shouldRedirect) {
          return null;
        }
        
        throw error; // Re-throw for component-level handling
      }
    };
  }, [handleCreditCardError]);

  return {
    handleCreditCardError,
    getCreditCardErrorMessage,
    showCreditCardErrorAlert,
    withCreditCardErrorHandling
  };
};

export default useCreditCardErrorHandler;
