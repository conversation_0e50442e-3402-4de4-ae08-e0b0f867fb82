/**
 * Tela de histórico de pagamentos
 * Lista todos os pagamentos do usuário com filtros e detalhes
 */

import React, {useCallback, useState, useMemo} from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  FlatList
} from "react-native";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import {SafeAreaView} from "react-native-safe-area-context";

import ScreenWithHeader from "../../components/screen-with-header";
import LoadingOverlay from "../../components/loading-overlay";
import Button from "../../components/button";

import {
  Payment,
  PaymentStatus,
  PaymentType
} from "../../models/api/payments.models";

import {useUserPayments} from "../../hooks/api/use-payments";
import styles from "../../styles/screens/payment-history.style";

import CheckIcon from "../../components/icons/check-icon";
import ClockIcon from "../../components/icons/clock-icon";
import ErrorIcon from "../../components/icons/error-icon";
import CreditCardIcon from "../../components/icons/credit-card-icon";
import PixIcon from "../../components/icons/pix-icon";
import BoletoIcon from "../../components/icons/boleto-icon";

const PaymentHistoryScreen: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();

  // Estados locais
  const [selectedStatus, setSelectedStatus] = useState<
    PaymentStatus | undefined
  >();
  const [refreshing, setRefreshing] = useState(false);

  // Hook para buscar pagamentos
  const {
    data: payments = [],
    isLoading,
    error,
    refetch
  } = useUserPayments(1, 50, selectedStatus);

  console.log("📋 [PAYMENT-HISTORY] Estado:", {
    paymentsCount: payments.length,
    selectedStatus,
    isLoading,
    error: error?.message
  });

  // Handler para refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  // Handler para ver detalhes do pagamento
  const handleViewPayment = useCallback(
    (payment: Payment) => {
      console.log("👁️ [PAYMENT-HISTORY] Visualizando pagamento:", payment.id);
      router.push({
        pathname: "/(logged-stack)/payment-confirmation",
        params: {paymentId: payment.id}
      });
    },
    [router]
  );

  // Formatar valor monetário
  const formatCurrency = useCallback((value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL"
    }).format(value);
  }, []);

  // Formatar data
  const formatDate = useCallback((dateString: string) => {
    return new Intl.DateTimeFormat("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(new Date(dateString));
  }, []);

  // Renderizar ícone do status
  const renderStatusIcon = useCallback((status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.Confirmed:
        return <CheckIcon style={styles.statusIconSuccess} />;
      case PaymentStatus.Pending:
      case PaymentStatus.Created:
        return <ClockIcon style={styles.statusIconPending} />;
      case PaymentStatus.Failed:
      case PaymentStatus.Unknown:
        return <ErrorIcon style={styles.statusIconError} />;
      default:
        return <ClockIcon style={styles.statusIconPending} />;
    }
  }, []);

  // Renderizar ícone do tipo de pagamento
  const renderPaymentTypeIcon = useCallback((type: PaymentType) => {
    const iconProps = {
      width: styles.paymentTypeIcon.width,
      height: styles.paymentTypeIcon.height,
      color: styles.paymentTypeIcon.color
    };

    switch (type) {
      case PaymentType.CreditCard:
        return <CreditCardIcon {...iconProps} />;
      case PaymentType.Pix:
        return <PixIcon size={iconProps.width} color={iconProps.color} />;
      case PaymentType.Boleto:
        return <BoletoIcon size={iconProps.width} color={iconProps.color} />;
      default:
        return <CreditCardIcon {...iconProps} />;
    }
  }, []);

  // Renderizar item de pagamento
  const renderPaymentItem = useCallback(
    ({item: payment}: {item: Payment}) => (
      <TouchableOpacity
        style={styles.paymentItem}
        onPress={() => handleViewPayment(payment)}
      >
        <View style={styles.paymentHeader}>
          <View style={styles.paymentTypeContainer}>
            {renderPaymentTypeIcon(payment.type)}
            <Text style={styles.paymentType}>
              {t(`payment.types.${payment.type}`)}
            </Text>
          </View>
          <View style={styles.statusContainer}>
            {renderStatusIcon(payment.status)}
            <Text
              style={[
                styles.statusText,
                payment.status === PaymentStatus.Confirmed &&
                  styles.statusTextSuccess,
                payment.status === PaymentStatus.Failed &&
                  styles.statusTextError
              ]}
            >
              {t(`payment.status.${payment.status}`)}
            </Text>
          </View>
        </View>

        <View style={styles.paymentDetails}>
          <Text style={styles.paymentAmount}>
            {formatCurrency(payment.amount)}
          </Text>
          <Text style={styles.paymentDate}>
            {formatDate(payment.createdAt)}
          </Text>
        </View>

        <View style={styles.paymentInfo}>
          <Text style={styles.paymentId}>ID: {payment.id}</Text>
          {payment.transactionId && (
            <Text style={styles.transactionId}>
              TXN: {payment.transactionId}
            </Text>
          )}
        </View>
      </TouchableOpacity>
    ),
    [
      handleViewPayment,
      renderPaymentTypeIcon,
      renderStatusIcon,
      formatCurrency,
      formatDate,
      t
    ]
  );

  // Filtros de status
  const statusFilters = useMemo(
    () => [
      {key: undefined, label: t("paymentHistory.filters.all")},
      {key: PaymentStatus.Confirmed, label: t("payment.status.Confirmed")},
      {key: PaymentStatus.Pending, label: t("payment.status.Pending")},
      {key: PaymentStatus.Failed, label: t("payment.status.Failed")}
    ],
    [t]
  );

  // Renderizar filtros
  const renderFilters = () => (
    <ScrollView
      horizontal
      style={styles.filtersContainer}
      showsHorizontalScrollIndicator={false}
    >
      {statusFilters.map((filter) => (
        <TouchableOpacity
          key={filter.key || "all"}
          style={[
            styles.filterButton,
            selectedStatus === filter.key && styles.filterButtonActive
          ]}
          onPress={() => setSelectedStatus(filter.key)}
        >
          <Text
            style={[
              styles.filterButtonText,
              selectedStatus === filter.key && styles.filterButtonTextActive
            ]}
          >
            {filter.label}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  // Estado de loading
  if (isLoading && !refreshing) {
    return (
      <ScreenWithHeader screenTitle={t("paymentHistory.title")}>
        <LoadingOverlay message={t("paymentHistory.loading")} />
      </ScreenWithHeader>
    );
  }

  // Estado de erro
  if (error) {
    return (
      <ScreenWithHeader screenTitle={t("paymentHistory.title")}>
        <View style={styles.errorContainer}>
          <ErrorIcon style={styles.errorIcon} />
          <Text style={styles.errorTitle}>
            {t("paymentHistory.error.title")}
          </Text>
          <Text style={styles.errorMessage}>
            {t("paymentHistory.error.message")}
          </Text>
          <Button
            text="common.tryAgain"
            onPress={() => refetch()}
            style={styles.retryButton}
          />
        </View>
      </ScreenWithHeader>
    );
  }

  return (
    <ScreenWithHeader screenTitle={t("paymentHistory.title")}>
      <View style={styles.container}>
        {renderFilters()}

        {payments.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyTitle}>
              {t("paymentHistory.empty.title")}
            </Text>
            <Text style={styles.emptyMessage}>
              {t("paymentHistory.empty.message")}
            </Text>
          </View>
        ) : (
          <FlatList
            data={payments}
            renderItem={renderPaymentItem}
            keyExtractor={(item) => item.id}
            style={styles.paymentsList}
            contentContainerStyle={styles.paymentsListContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
              />
            }
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </ScreenWithHeader>
  );
};

export default PaymentHistoryScreen;
