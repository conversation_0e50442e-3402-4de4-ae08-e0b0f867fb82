import {StyleSheet, Dimensions} from "react-native";
import stylesConstants from "../../styles-constants";

const {height: screenHeight} = Dimensions.get("window");

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "flex-end"
  },
  backdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "black"
  },
  backdropTouchable: {
    flex: 1
  },
  drawer: {
    backgroundColor: "#111828",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    borderTopWidth: 1.5,
    borderLeftWidth: 1.5,
    borderRightWidth: 1.5,
    borderColor: "#282A2E",
    maxHeight: screenHeight * 0.8,
    minHeight: 312
  },
  safeArea: {
    flex: 1
  },
  header: {
    alignItems: "center",
    paddingTop: 8,
    paddingBottom: 16,
    gap: 24
  },
  handle: {
    width: 44,
    height: 4,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 2
  },
  title: {
    color: "#DFE9F0",
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center"
  },
  content: {
    flex: 1,
    paddingHorizontal: 16
  },
  section: {
    backgroundColor: "#202938",
    borderWidth: 1,
    borderColor: "#282A2E",
    borderRadius: 8,
    padding: 16,
    gap: 16
  },
  sectionTitle: {
    color: "#DFE9F0",
    fontSize: 12,
    lineHeight: 18
  },
  filtersContainer: {
    gap: 16
  },
  filtersRow: {
    flexDirection: "row",
    gap: 16
  },
  filterBadge: {
    borderWidth: 1,
    borderColor: "#DFE9F0",
    borderRadius: 16,
    paddingHorizontal: 8,
    paddingVertical: 2,
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    flex: 1
  },
  filterBadgeSelected: {
    backgroundColor: "#F9FAFB",
    borderColor: "#FFFFFF"
  },
  filterBadgeText: {
    color: "#DFE9F0",
    fontSize: 14,
    lineHeight: 20,
    textAlign: "center",
    flex: 1
  },
  filterBadgeTextSelected: {
    color: "#1D2939"
  },
  buttonsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 28
  },
  clearButton: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16
  },
  clearButtonText: {
    color: "#DFE9F0",
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  }
});

export default styles;
