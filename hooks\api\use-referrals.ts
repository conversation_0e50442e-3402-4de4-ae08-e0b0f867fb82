/**
 * Custom hooks for referrals/indications API operations
 * Provides React Query integration for referral management
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
  UseQueryOptions,
  UseMutationOptions,
  UseInfiniteQueryOptions
} from "@tanstack/react-query";
import {ReferralsService} from "@/services/api/referrals/referrals.service";
import {
  Referral,
  ReferralsListParams,
  ReferralsResponse,
  CreateReferralRequest,
  ReferralStats,
  ContactSearchParams,
  ContactSearchResponse,
  BulkReferralRequest,
  BulkReferralResponse,
  ResendInvitationRequest
} from "@/models/api/referrals.models";

// Query keys for referrals
export const referralsKeys = {
  all: ["referrals"] as const,
  lists: () => [...referralsKeys.all, "list"] as const,
  list: (params: ReferralsListParams) =>
    [...referralsKeys.lists(), params] as const,
  details: () => [...referralsKeys.all, "detail"] as const,
  detail: (id: number) => [...referralsKeys.details(), id] as const,
  stats: () => [...referralsKeys.all, "stats"] as const,
  contacts: () => ["contacts"] as const,
  contactSearch: (params: ContactSearchParams) =>
    [...referralsKeys.contacts(), "search", params] as const
};

/**
 * Hook to get paginated list of referrals
 */
export function useReferrals(
  params?: ReferralsListParams,
  options?: UseQueryOptions<ReferralsResponse>
) {
  return useQuery({
    queryKey: referralsKeys.list(params || {}),
    queryFn: () => ReferralsService.getReferrals(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options
  });
}

/**
 * Hook to get infinite paginated list of referrals
 */
export function useInfiniteReferrals(
  params?: Omit<ReferralsListParams, "page">,
  options?: UseInfiniteQueryOptions<ReferralsResponse>
) {
  return useInfiniteQuery({
    initialPageParam: 1,
    queryKey: referralsKeys.list(params || {}),
    queryFn: ({pageParam = 1}) =>
      ReferralsService.getReferrals({...params, page: pageParam as number}),
    getNextPageParam: (lastPage) =>
      lastPage.pagination.hasNextPage
        ? lastPage.pagination.currentPage + 1
        : undefined,
    staleTime: 5 * 60 * 1000,
    ...options
  });
}

/**
 * Hook to get a specific referral by ID
 */
export function useReferral(id: number, options?: UseQueryOptions<Referral>) {
  return useQuery({
    queryKey: referralsKeys.detail(id),
    queryFn: () => ReferralsService.getReferral(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    ...options
  });
}

/**
 * Hook to get referral statistics
 */
export function useReferralStats(options?: UseQueryOptions<ReferralStats>) {
  return useQuery({
    queryKey: referralsKeys.stats(),
    queryFn: () => ReferralsService.getReferralStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options
  });
}

/**
 * Hook to search for contacts
 */
export function useContactSearch(
  params: ContactSearchParams,
  options?: UseQueryOptions<ContactSearchResponse>
) {
  return useQuery({
    queryKey: referralsKeys.contactSearch(params),
    queryFn: () => ReferralsService.searchContacts(params),
    enabled: !!params.search && params.search.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options
  });
}

/**
 * Hook to create a new referral
 */
export function useCreateReferral(
  options?: UseMutationOptions<Referral, Error, CreateReferralRequest>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateReferralRequest) =>
      ReferralsService.createReferral(data),
    onSuccess: (newReferral) => {
      // Invalidate and refetch referrals list
      queryClient.invalidateQueries({queryKey: referralsKeys.lists()});
      queryClient.invalidateQueries({queryKey: referralsKeys.stats()});

      // Add the new referral to the cache
      queryClient.setQueryData(
        referralsKeys.detail(newReferral.id),
        newReferral
      );
    },
    ...options
  });
}

/**
 * Hook to create multiple referrals at once
 */
export function useCreateBulkReferrals(
  options?: UseMutationOptions<BulkReferralResponse, Error, BulkReferralRequest>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkReferralRequest) =>
      ReferralsService.createBulkReferrals(data),
    onSuccess: () => {
      // Invalidate and refetch referrals list and stats
      queryClient.invalidateQueries({queryKey: referralsKeys.lists()});
      queryClient.invalidateQueries({queryKey: referralsKeys.stats()});
    },
    ...options
  });
}

/**
 * Hook to resend invitation for a referral
 */
export function useResendInvitation(
  options?: UseMutationOptions<void, Error, ResendInvitationRequest>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ResendInvitationRequest) =>
      ReferralsService.resendInvitation(data),
    onSuccess: (_, variables) => {
      // Invalidate the specific referral and lists
      queryClient.invalidateQueries({
        queryKey: referralsKeys.detail(variables.referralId)
      });
      queryClient.invalidateQueries({queryKey: referralsKeys.lists()});
    },
    ...options
  });
}

/**
 * Hook to cancel a referral
 */
export function useCancelReferral(
  options?: UseMutationOptions<void, Error, number>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => ReferralsService.cancelReferral(id),
    onSuccess: (_, referralId) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({queryKey: referralsKeys.detail(referralId)});
      queryClient.invalidateQueries({queryKey: referralsKeys.lists()});
      queryClient.invalidateQueries({queryKey: referralsKeys.stats()});
    },
    ...options
  });
}

/**
 * Hook to invalidate referrals cache
 */
export function useInvalidateReferrals() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({queryKey: referralsKeys.all});
  };
}

/**
 * Hook to get user by document
 */
export function useUserByDocument(
  document: string,
  options?: UseQueryOptions<any>
) {
  return useQuery({
    queryKey: ["users", "document", document],
    queryFn: () => ReferralsService.getUserByDocument(document),
    enabled: !!document && document.length >= 11, // CPF minimum length
    staleTime: 5 * 60 * 1000,
    ...options
  });
}
