{"name": "Club M", "slug": "club-m-app", "scheme": "clubm", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "studio.takasaki.clubm", "googleServicesFile": "./keys/GoogleService-Info.plist"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "studio.takasaki.clubm", "googleServicesFile": "./keys/google-services.json", "permissions": ["android.permission.USE_BIOMETRIC", "android.permission.USE_FINGERPRINT"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"backgroundColor": "#0C111D", "image": "./assets/splash-icon.png", "imageWidth": 100}], "expo-localization", ["expo-font", {"fonts": ["./assets/fonts/open-sans/OpenSans-Italic-VariableFont_wdth,wght.ttf", "./assets/fonts/open-sans/OpenSans-VariableFont_wdth,wght.ttf", "./assets/fonts/inter/Inter-Italic-VariableFont_opsz,wght.ttf", "./assets/fonts/inter/Inter-VariableFont_opsz,wght.ttf"]}], ["expo-local-authentication", {"faceIDPermission": "Habilitar Face ID para\n“Club M Brasil”?"}], ["expo-secure-store", {"configureAndroidBackup": true, "faceIDPermission": "Habilitar Face ID para\n“Club M Brasil”?"}], "expo-web-browser"], "extra": {"router": {"origin": false}, "eas": {"projectId": "7a3dfdd1-6870-43b9-a315-758a26cd3fb1"}}, "sdkVersion": "53.0.0", "platforms": ["ios", "android"], "androidStatusBar": {"backgroundColor": "#0C111D"}}