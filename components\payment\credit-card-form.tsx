/**
 * Componente de formulário para cartão de crédito
 * Seguindo os padrões de design e validação do projeto
 */

import React, {useCallback, useMemo, useState} from "react";
import {View, Text} from "react-native";
import {useTranslation} from "react-i18next";
import InputField from "../input-field";
import Button from "../button";
import {
  CreateCreditCardFormData,
  CreateCreditCardSchema
} from "../../models/api/payments.models";
import {ZodError} from "zod";
import styles from "../../styles/components/payment/credit-card-form.style";
import CreditCardIcon from "../icons/credit-card-icon";

export interface CreditCardFormProps {
  onSubmit: (data: CreateCreditCardFormData) => void;
  onBack?: () => void; // Função para voltar à seleção de método
  initialData?: Partial<CreateCreditCardFormData>;
  isLoading?: boolean;
  submitButtonText?: string;
}

const CreditCardForm: React.FC<CreditCardFormProps> = ({
  onSubmit,
  onBack,
  initialData,
  isLoading = false,
  submitButtonText = "components.payment.creditCardForm.submit"
}) => {
  const {t} = useTranslation();

  // Estado do formulário - baseado no schema completo da API
  const [formData, setFormData] = useState<CreateCreditCardFormData>({
    // Dados do cartão
    number: initialData?.number || "",
    holderName: initialData?.holderName || "",
    expiryMonth:
      initialData?.expiryMonth ||
      String(new Date().getMonth() + 1).padStart(2, "0"),
    expiryYear: initialData?.expiryYear || String(new Date().getFullYear()),
    ccv: initialData?.ccv || "",

    // Dados pessoais obrigatórios
    name: initialData?.name || "",
    cpfCnpj: initialData?.cpfCnpj || "",
    phoneNumber: initialData?.phoneNumber || "",

    // Dados de endereço obrigatórios
    postalCode: initialData?.postalCode || "",
    addressNumber: initialData?.addressNumber || "",
    addressComplement: initialData?.addressComplement || ""
  });

  // Estado dos erros
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Função genérica para atualizar dados do formulário
  const updateFormData = useCallback(
    (field: keyof CreateCreditCardFormData, value: string) => {
      setFormData((prev) => ({...prev, [field]: value}));
      if (errors[field]) {
        setErrors((prev) => ({...prev, [field]: ""}));
      }
    },
    [errors]
  );

  // Função para formatar número do cartão
  const formatCardNumber = useCallback((value: string) => {
    const cleaned = value.replace(/\D/g, "");
    const formatted = cleaned.replace(/(\d{4})(?=\d)/g, "$1 ");
    return formatted.substring(0, 19); // 16 dígitos + 3 espaços
  }, []);

  // Função para formatar CVV
  const formatCVV = useCallback((value: string) => {
    return value.replace(/\D/g, "").substring(0, 4);
  }, []);

  // Função para formatar nome do portador
  const formatHolderName = useCallback((value: string) => {
    return value.toUpperCase().replace(/[^A-Z\s]/g, "");
  }, []);

  // Handlers de mudança de campo
  const handleCardNumberChange = useCallback(
    (value: string) => {
      const formatted = formatCardNumber(value);
      setFormData((prev) => ({...prev, number: formatted.replace(/\s/g, "")}));
      if (errors.number) {
        setErrors((prev) => ({...prev, number: ""}));
      }
    },
    [formatCardNumber, errors.number]
  );

  const handleHolderNameChange = useCallback(
    (value: string) => {
      const formatted = formatHolderName(value);
      setFormData((prev) => ({...prev, holderName: formatted}));
      if (errors.holderName) {
        setErrors((prev) => ({...prev, holderName: ""}));
      }
    },
    [formatHolderName, errors.holderName]
  );

  const handleExpirationMonthChange = useCallback(
    (value: string) => {
      const month = value.padStart(2, "0");
      if (parseInt(month) >= 1 && parseInt(month) <= 12) {
        setFormData((prev) => ({...prev, expiryMonth: month}));
        if (errors.expiryMonth) {
          setErrors((prev) => ({...prev, expiryMonth: ""}));
        }
      }
    },
    [errors.expiryMonth]
  );

  const handleExpirationYearChange = useCallback(
    (value: string) => {
      setFormData((prev) => ({...prev, expiryYear: value}));
      if (errors.expiryYear) {
        setErrors((prev) => ({...prev, expiryYear: ""}));
      }
    },
    [errors.expiryYear]
  );

  const handleCVVChange = useCallback(
    (value: string) => {
      const formatted = formatCVV(value);
      setFormData((prev) => ({...prev, ccv: formatted}));
      if (errors.ccv) {
        setErrors((prev) => ({...prev, ccv: ""}));
      }
    },
    [formatCVV, errors.ccv]
  );

  // Validação do formulário com debug detalhado
  const validateForm = useCallback(() => {
    console.log(
      "🔍 [CREDIT-CARD-FORM] 🔧 DEBUG VALIDAÇÃO - Iniciando validação"
    );
    console.log(
      "📋 [CREDIT-CARD-FORM] 🔧 DEBUG VALIDAÇÃO - Dados para validar:",
      {
        number: formData.number,
        numberLength: formData.number.length,
        numberCleaned: formData.number.replace(/\D/g, ""),
        numberCleanedLength: formData.number.replace(/\D/g, "").length,
        holderName: formData.holderName,
        holderNameLength: formData.holderName.length,
        expiryMonth: formData.expiryMonth,
        expiryYear: formData.expiryYear,
        ccv: formData.ccv,
        ccvLength: formData.ccv.length,
        name: formData.name,
        nameLength: formData.name.length,
        cpfCnpj: formData.cpfCnpj,
        cpfCnpjLength: formData.cpfCnpj.length,
        cpfCnpjCleaned: formData.cpfCnpj.replace(/\D/g, ""),
        cpfCnpjCleanedLength: formData.cpfCnpj.replace(/\D/g, "").length,
        phoneNumber: formData.phoneNumber,
        phoneNumberLength: formData.phoneNumber.length,
        phoneNumberCleaned: formData.phoneNumber.replace(/\D/g, ""),
        phoneNumberCleanedLength: formData.phoneNumber.replace(/\D/g, "")
          .length,
        postalCode: formData.postalCode,
        postalCodeLength: formData.postalCode.length,
        postalCodeCleaned: formData.postalCode.replace(/\D/g, ""),
        postalCodeCleanedLength: formData.postalCode.replace(/\D/g, "").length,
        addressNumber: formData.addressNumber,
        addressNumberLength: formData.addressNumber.length,
        addressComplement: formData.addressComplement,
        addressComplementLength: formData.addressComplement.length
      }
    );

    try {
      CreateCreditCardSchema.parse(formData);
      console.log(
        "✅ [CREDIT-CARD-FORM] 🔧 DEBUG VALIDAÇÃO - Validação passou!"
      );
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof ZodError) {
        const newErrors: Record<string, string> = {};
        console.log(
          "❌ [CREDIT-CARD-FORM] 🔧 DEBUG VALIDAÇÃO - Erros encontrados:"
        );

        error.errors.forEach((err, index) => {
          console.log(
            `❌ [CREDIT-CARD-FORM] 🔧 DEBUG VALIDAÇÃO - Erro ${index + 1}:`,
            {
              campo: err.path.join("."),
              mensagem: err.message,
              codigo: err.code,
              input: (err as any).input || "N/A"
            }
          );

          const field = err.path[0] as string;
          newErrors[field] = err.message; // Usar mensagem original para debug
        });

        setErrors(newErrors);
        console.log(
          "❌ [CREDIT-CARD-FORM] 🔧 DEBUG VALIDAÇÃO - Resumo dos erros:",
          newErrors
        );
      }
      return false;
    }
  }, [formData]);

  // Handler de submit
  const handleSubmit = useCallback(() => {
    console.log("💳 [CREDIT-CARD-FORM] 🔧 TESTE PÓS-CORREÇÃO BACKEND");
    console.log("📋 [CREDIT-CARD-FORM] Dados do formulário:", {
      holderName: formData.holderName,
      cardNumber: formData.number.slice(-4), // Só últimos 4 dígitos
      cardNumberLength: formData.number.length,
      expiryMonth: formData.expiryMonth,
      expiryYear: formData.expiryYear,
      ccvLength: formData.ccv.length,
      name: formData.name,
      cpfCnpj: formData.cpfCnpj,
      phoneNumber: formData.phoneNumber,
      postalCode: formData.postalCode
    });

    if (validateForm()) {
      console.log("✅ [CREDIT-CARD-FORM] Validação passou, submetendo");

      // Limpa os dados antes de enviar
      const cleanedData = {
        ...formData,
        number: formData.number.replace(/\D/g, ""), // Remove caracteres não numéricos
        holderName: formData.holderName.trim(),
        ccv: formData.ccv.replace(/\D/g, ""), // Remove caracteres não numéricos
        cpfCnpj: formData.cpfCnpj.replace(/\D/g, ""), // Remove caracteres não numéricos
        phoneNumber: formData.phoneNumber.replace(/\D/g, ""), // Remove caracteres não numéricos
        postalCode: formData.postalCode.replace(/\D/g, "") // Remove caracteres não numéricos
      };

      console.log("📋 [CREDIT-CARD-FORM] Dados limpos para envio:", {
        holderName: cleanedData.holderName,
        cardNumberLength: cleanedData.number.length,
        lastFourDigits: cleanedData.number.slice(-4),
        expiryMonth: cleanedData.expiryMonth,
        expiryYear: cleanedData.expiryYear,
        ccvLength: cleanedData.ccv.length,
        name: cleanedData.name,
        cpfCnpjLength: cleanedData.cpfCnpj.length,
        phoneNumberLength: cleanedData.phoneNumber.length,
        postalCodeLength: cleanedData.postalCode.length
      });

      onSubmit(cleanedData);
    } else {
      console.log("❌ [CREDIT-CARD-FORM] Validação falhou");
    }
  }, [validateForm, onSubmit, formData]);

  // Valor formatado do número do cartão para exibição
  const displayCardNumber = useMemo(() => {
    return formatCardNumber(formData.number);
  }, [formData.number, formatCardNumber]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        {t("components.payment.creditCardForm.title")}
      </Text>

      <InputField
        label={t("components.payment.creditCardForm.cardNumber")}
        placeholder="0000 0000 0000 0000"
        value={displayCardNumber}
        onChangeText={handleCardNumberChange}
        error={errors.number}
        inputMode="numeric"
        maxLength={19}
        icon={() => <CreditCardIcon />}
        style={styles.inputField}
      />

      <InputField
        label={t("components.payment.creditCardForm.holderName")}
        placeholder={t(
          "components.payment.creditCardForm.holderNamePlaceholder"
        )}
        value={formData.holderName}
        onChangeText={handleHolderNameChange}
        error={errors.holderName}
        style={styles.inputField}
      />

      <View style={styles.row}>
        <View style={styles.halfWidth}>
          <InputField
            label={t("components.payment.creditCardForm.expirationMonth")}
            placeholder="MM"
            value={formData.expiryMonth.padStart(2, "0")}
            onChangeText={handleExpirationMonthChange}
            error={errors.expiryMonth}
            inputMode="numeric"
            maxLength={2}
            style={styles.inputField}
          />
        </View>

        <View style={styles.halfWidth}>
          <InputField
            label={t("components.payment.creditCardForm.expirationYear")}
            placeholder="AAAA"
            value={formData.expiryYear}
            onChangeText={handleExpirationYearChange}
            error={errors.expiryYear}
            inputMode="numeric"
            maxLength={4}
            style={styles.inputField}
          />
        </View>
      </View>

      <InputField
        label={t("components.payment.creditCardForm.cvv")}
        placeholder="000"
        value={formData.ccv}
        onChangeText={handleCVVChange}
        error={errors.ccv}
        inputMode="numeric"
        maxLength={4}
        isPassword
        style={styles.inputField}
      />

      {/* Seção de dados pessoais */}
      <Text style={styles.title}>
        {t("components.payment.creditCardForm.personalData")}
      </Text>

      <InputField
        label={t("components.payment.creditCardForm.fullName")}
        placeholder="Nome completo"
        value={formData.name}
        onChangeText={(value) => updateFormData("name", value)}
        error={errors.name}
        style={styles.inputField}
      />

      <InputField
        label={t("components.payment.creditCardForm.cpfCnpj")}
        placeholder="000.000.000-00"
        value={formData.cpfCnpj}
        onChangeText={(value) => updateFormData("cpfCnpj", value)}
        error={errors.cpfCnpj}
        inputMode="numeric"
        style={styles.inputField}
      />

      <InputField
        label={t("components.payment.creditCardForm.phoneNumber")}
        placeholder="(11) 99999-9999"
        value={formData.phoneNumber}
        onChangeText={(value) => updateFormData("phoneNumber", value)}
        error={errors.phoneNumber}
        inputMode="tel"
        style={styles.inputField}
      />

      {/* Seção de endereço */}
      <Text style={styles.title}>
        {t("components.payment.creditCardForm.addressData")}
      </Text>

      <InputField
        label={t("components.payment.creditCardForm.postalCode")}
        placeholder="00000-000"
        value={formData.postalCode}
        onChangeText={(value) => updateFormData("postalCode", value)}
        error={errors.postalCode}
        inputMode="numeric"
        maxLength={9}
        style={styles.inputField}
      />

      <InputField
        label={t("components.payment.creditCardForm.addressNumber")}
        placeholder="123"
        value={formData.addressNumber}
        onChangeText={(value) => updateFormData("addressNumber", value)}
        error={errors.addressNumber}
        style={styles.inputField}
      />

      <InputField
        label={t("components.payment.creditCardForm.addressComplement")}
        placeholder="Apto 45, Bloco B (opcional)"
        value={formData.addressComplement}
        onChangeText={(value) => updateFormData("addressComplement", value)}
        error={errors.addressComplement}
        style={styles.inputField}
      />

      {/* Botões de ação */}
      {onBack && (
        <Button
          text={t("components.payment.creditCardForm.back")}
          onPress={onBack}
          style={[
            styles.submitButton,
            {backgroundColor: "#6B7280", marginBottom: 10}
          ]}
        />
      )}

      <Button
        text={submitButtonText}
        onPress={handleSubmit}
        style={styles.submitButton}
      />
    </View>
  );
};

export default CreditCardForm;
