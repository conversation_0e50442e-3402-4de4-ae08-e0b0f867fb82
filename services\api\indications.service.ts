import {ApiResponse} from "@/models/api/common.models";
import {
  CreateIndicationRequest,
  CreateIndicationResponse,
  Indication,
  IndicationStats,
  IndicationsListParams,
  IndicationsListResponse
} from "@/models/api/indications.models";
import {apiClient} from "@/services/api/base/api-client";
import {firstValueFrom} from "rxjs";

/**
 * Serviço para gerenciar indicações
 */
export class IndicationsService {
  private static readonly BASE_PATH = "/api/app/indications";

  /**
   * Cria uma nova indicação
   */
  static async createIndication(
    data: CreateIndicationRequest
  ): Promise<ApiResponse<CreateIndicationResponse>> {
    try {
      const response = await firstValueFrom(
        apiClient.post<CreateIndicationResponse>(this.BASE_PATH, data)
      );

      return {
        success: true,
        data: response,
        message: "Indicação criada com sucesso!"
      };
    } catch (error: any) {
      console.error("❌ [INDICATIONS] Erro ao criar indicação:", error);

      return {
        success: false,
        data: null as any,
        message:
          error.response?.data?.message ||
          error.message ||
          "Erro ao criar indicação. Tente novamente.",
        errors: error.response?.data || error.message
      };
    }
  }

  /**
   * Lista as indicações do usuário
   */
  static async getIndications(
    params?: IndicationsListParams
  ): Promise<ApiResponse<IndicationsListResponse>> {
    try {
      const response = await firstValueFrom(
        apiClient.get<IndicationsListResponse>(this.BASE_PATH, params)
      );

      return {
        success: true,
        data: response,
        message: "Indicações carregadas com sucesso!"
      };
    } catch (error: any) {
      console.error("❌ [INDICATIONS] Erro ao carregar indicações:", error);

      return {
        success: false,
        data: null as any,
        message:
          error.response?.data?.message ||
          error.message ||
          "Erro ao carregar indicações. Tente novamente.",
        errors: error.response?.data || error.message
      };
    }
  }

  /**
   * Busca uma indicação específica por ID
   */
  static async getIndicationById(id: string): Promise<ApiResponse<Indication>> {
    try {
      const response = await firstValueFrom(
        apiClient.get<Indication>(`${this.BASE_PATH}/${id}`)
      );

      return {
        success: true,
        data: response,
        message: "Indicação carregada com sucesso!"
      };
    } catch (error: any) {
      console.error("❌ [INDICATIONS] Erro ao carregar indicação:", error);

      return {
        success: false,
        data: null as any,
        message:
          error.response?.data?.message ||
          error.message ||
          "Erro ao carregar indicação. Tente novamente.",
        errors: error.response?.data || error.message
      };
    }
  }

  /**
   * Busca estatísticas de indicações
   */
  static async getIndicationStats(): Promise<ApiResponse<IndicationStats>> {
    try {
      const response = await firstValueFrom(
        apiClient.get<IndicationStats>(`${this.BASE_PATH}/stats`)
      );

      return {
        success: true,
        data: response,
        message: "Estatísticas carregadas com sucesso!"
      };
    } catch (error: any) {
      console.error("❌ [INDICATIONS] Erro ao carregar estatísticas:", error);

      return {
        success: false,
        data: null as any,
        message:
          error.response?.data?.message ||
          error.message ||
          "Erro ao carregar estatísticas. Tente novamente.",
        errors: error.response?.data || error.message
      };
    }
  }
}
