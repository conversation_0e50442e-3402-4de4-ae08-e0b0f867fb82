/**
 * Serviço de pagamentos do ClubM
 * Seguindo os padrões estabelecidos no projeto
 */

import {ApiResponse} from "../../../models/api/common.models";
import {
  Payment,
  CreatePaymentRequest,
  CreateCreditCardRequest,
  CreditCard,
  PaymentStatus
} from "../../../models/api/payments.models";
import {apiClient} from "../base/api-client";
import {firstValueFrom} from "rxjs";

export class PaymentsService {
  private static readonly PAYMENTS_BASE_PATH = "/api/app/payments";
  private static readonly CREDIT_CARDS_BASE_PATH = "/api/app/creditCards";

  /**
   * Cria um novo pagamento
   */
  static async createPayment(
    paymentData: CreatePaymentRequest
  ): Promise<ApiResponse<Payment>> {
    console.log("🌐 [PAYMENTS-SERVICE] Criando pagamento");
    console.log("📤 [PAYMENTS-SERVICE] URL:", this.PAYMENTS_BASE_PATH);
    console.log("📋 [PAYMENTS-SERVICE] Payload:", paymentData);

    const response = await firstValueFrom(
      apiClient.post<Payment>(this.PAYMENTS_BASE_PATH, paymentData)
    );

    console.log("📥 [PAYMENTS-SERVICE] Resposta da API:", response);
    return {
      success: true,
      data: response
    };
  }

  /**
   * Busca um pagamento por ID
   */
  static async getPaymentById(id: string): Promise<ApiResponse<Payment>> {
    console.log("🌐 [PAYMENTS-SERVICE] Buscando pagamento por ID:", id);

    const response = await firstValueFrom(
      apiClient.get<Payment>(`${this.PAYMENTS_BASE_PATH}/${id}`)
    );
    return {
      success: true,
      data: response
    };
  }

  /**
   * Lista pagamentos do usuário
   */
  static async getUserPayments(
    page: number = 1,
    pageSize: number = 10,
    status?: PaymentStatus,
    entity?: number,
    entityId?: number
  ): Promise<ApiResponse<Payment[]>> {
    console.log("🌐 [PAYMENTS-SERVICE] Listando pagamentos do usuário");

    const params = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString(),
      ...(status && {status}),
      ...(entity !== undefined && {entity: entity.toString()}),
      ...(entityId !== undefined && {entityId: entityId.toString()})
    });

    const response = await firstValueFrom(
      apiClient.get<Payment[]>(
        `${this.PAYMENTS_BASE_PATH}?${params.toString()}`
      )
    );
    return {
      success: true,
      data: response
    };
  }

  /**
   * Cancela um pagamento
   */
  static async cancelPayment(id: string): Promise<ApiResponse<Payment>> {
    const response = await firstValueFrom(
      apiClient.patch<Payment>(`${this.PAYMENTS_BASE_PATH}/${id}/cancel`)
    );
    return {
      success: true,
      data: response
    };
  }

  /**
   * Confirma um pagamento PIX
   */
  static async confirmPixPayment(id: string): Promise<ApiResponse<Payment>> {
    const response = await firstValueFrom(
      apiClient.patch<Payment>(`${this.PAYMENTS_BASE_PATH}/${id}/confirm-pix`)
    );
    return {
      success: true,
      data: response
    };
  }

  /**
   * Busca QR Code do pagamento
   */
  static async getPaymentQRCode(id: string): Promise<ApiResponse<string>> {
    console.log("🌐 [PAYMENTS-SERVICE] Buscando QR Code do pagamento:", id);

    const response = await firstValueFrom(
      apiClient.get<string>(`${this.PAYMENTS_BASE_PATH}/${id}/qr-code`)
    );
    return {
      success: true,
      data: response
    };
  }

  /**
   * Busca código de barras do pagamento
   */
  static async getPaymentBarCode(id: string): Promise<ApiResponse<string>> {
    console.log(
      "🌐 [PAYMENTS-SERVICE] Buscando código de barras do pagamento:",
      id
    );

    const response = await firstValueFrom(
      apiClient.get<string>(`${this.PAYMENTS_BASE_PATH}/${id}/bar-code`)
    );
    return {
      success: true,
      data: response
    };
  }

  /**
   * Busca cartões de crédito salvos do usuário
   */
  static async getUserCreditCards(): Promise<ApiResponse<CreditCard[]>> {
    console.log(
      "🌐 [PAYMENTS-SERVICE] 🔧 CORREÇÃO CRÍTICA - Buscando cartões de crédito salvos"
    );
    console.log("📤 [PAYMENTS-SERVICE] URL:", this.CREDIT_CARDS_BASE_PATH);

    // A API retorna uma resposta paginada, não um array direto
    interface PaginatedCreditCardsResponse {
      data: CreditCard[];
      hasNextPage: boolean;
      hasPreviousPage: boolean;
      page: number;
      pageCount: number;
      pageSize: number;
      totalItems: number;
    }

    const response = await firstValueFrom(
      apiClient.get<PaginatedCreditCardsResponse>(this.CREDIT_CARDS_BASE_PATH)
    );

    console.log(
      "📥 [PAYMENTS-SERVICE] 🔧 CORREÇÃO CRÍTICA - Resposta bruta da API:",
      {
        type: typeof response,
        hasDataProperty: "data" in response,
        isPaginated: "page" in response && "totalItems" in response,
        response: response
      }
    );

    // A resposta da API é paginada: { data: CreditCard[], page: number, ... }
    const cards = response.data || [];

    console.log(
      "📥 [PAYMENTS-SERVICE] 🔧 CORREÇÃO CRÍTICA - Cartões processados:",
      {
        cardsCount: cards.length,
        totalItems: response.totalItems,
        page: response.page,
        cards: cards.map((card) => ({
          id: card.id,
          lastFourDigits: card.lastFourDigits || card.number?.slice(-4),
          brand: card.brand,
          userId: card.userId
        }))
      }
    );

    return {
      success: true,
      data: cards
    };
  }

  /**
   * Busca um cartão de crédito específico
   */
  static async getCreditCard(id: string): Promise<ApiResponse<CreditCard>> {
    console.log("🌐 [PAYMENTS-SERVICE] Buscando cartão de crédito:", id);

    const response = await firstValueFrom(
      apiClient.get<CreditCard>(`${this.CREDIT_CARDS_BASE_PATH}/${id}`)
    );
    return {
      success: true,
      data: response
    };
  }

  /**
   * Adiciona um novo cartão de crédito
   */
  static async createCreditCard(
    creditCardData: CreateCreditCardRequest
  ): Promise<ApiResponse<CreditCard>> {
    console.log(
      "🌐 [PAYMENTS-SERVICE] 🔧 CORREÇÃO API - Adicionando cartão de crédito"
    );
    console.log("📤 [PAYMENTS-SERVICE] URL:", this.CREDIT_CARDS_BASE_PATH);

    // Limpa e formata os dados conforme schema da API ClubM
    const cleanedData = {
      // Dados do cartão
      holderName: creditCardData.holderName.toUpperCase().trim(),
      number: creditCardData.number.replace(/\D/g, ""),
      expiryMonth: creditCardData.expiryMonth, // API usa string
      expiryYear: creditCardData.expiryYear, // API usa string
      ccv: creditCardData.ccv.replace(/\D/g, ""), // API usa "ccv"

      // Dados pessoais obrigatórios
      name: creditCardData.name.trim(),
      cpfCnpj: creditCardData.cpfCnpj.replace(/\D/g, ""),
      phoneNumber: creditCardData.phoneNumber.replace(/\D/g, ""),

      // Dados de endereço obrigatórios
      postalCode: creditCardData.postalCode.replace(/\D/g, ""),
      addressNumber: creditCardData.addressNumber.trim(),
      addressComplement: creditCardData.addressComplement?.trim() || ""
    };

    console.log("📋 [PAYMENTS-SERVICE] 🔧 CORREÇÃO API - Dados originais:", {
      holderName: creditCardData.holderName,
      numberLength: creditCardData.number.length,
      lastFourDigits: creditCardData.number.slice(-4),
      expiryMonth: creditCardData.expiryMonth,
      expiryYear: creditCardData.expiryYear,
      ccvLength: creditCardData.ccv.length,
      name: creditCardData.name,
      cpfCnpjLength: creditCardData.cpfCnpj.length,
      phoneNumberLength: creditCardData.phoneNumber.length,
      postalCodeLength: creditCardData.postalCode.length
    });

    console.log(
      "📋 [PAYMENTS-SERVICE] 🔧 CORREÇÃO API - Dados limpos para API:",
      {
        holderName: cleanedData.holderName,
        numberLength: cleanedData.number.length,
        lastFourDigits: cleanedData.number.slice(-4),
        expiryMonth: cleanedData.expiryMonth,
        expiryYear: cleanedData.expiryYear,
        ccvLength: cleanedData.ccv.length,
        name: cleanedData.name,
        cpfCnpjLength: cleanedData.cpfCnpj.length,
        phoneNumberLength: cleanedData.phoneNumber.length,
        postalCodeLength: cleanedData.postalCode.length,
        addressNumber: cleanedData.addressNumber,
        addressComplement: cleanedData.addressComplement
      }
    );

    const response = await firstValueFrom(
      apiClient.post<CreditCard>(this.CREDIT_CARDS_BASE_PATH, cleanedData)
    );

    console.log(
      "📥 [PAYMENTS-SERVICE] 🔧 CORREÇÃO API - Resposta da API:",
      response
    );
    return {
      success: true,
      data: response
    };
  }

  /**
   * Remove um cartão de crédito
   */
  static async deleteCreditCard(id: string): Promise<ApiResponse<void>> {
    console.log("🌐 [PAYMENTS-SERVICE] Removendo cartão de crédito:", id);

    const response = await firstValueFrom(
      apiClient.delete<void>(`${this.CREDIT_CARDS_BASE_PATH}/${id}`)
    );
    return {
      success: true,
      data: response
    };
  }
}

export default PaymentsService;
